#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要修复的文件和对应的修复规则
const fixes = [
  // scripts/migrate-venues-local.ts
  {
    file: 'scripts/migrate-venues-local.ts',
    replacements: [
      { from: '} catch (_error) {', to: '} catch {' },
    ]
  },
  
  // scripts/migrate-venues.ts
  {
    file: 'scripts/migrate-venues.ts',
    replacements: [
      { from: '} catch (_error) {', to: '} catch {' },
    ]
  },
  
  // scripts/validate-docs.ts
  {
    file: 'scripts/validate-docs.ts',
    replacements: [
      { from: 'import { join, dirname, resolve } from \'path\';', to: 'import { dirname, resolve } from \'path\';' },
    ]
  },
  
  // src/modules/auth/routes.ts
  {
    file: 'src/modules/auth/routes.ts',
    replacements: [
      { from: 'successResponse,', to: '' },
    ]
  },
  
  // src/modules/circle/tests/circles.integration.test.ts
  {
    file: 'src/modules/circle/tests/circles.integration.test.ts',
    replacements: [
      { from: '(args) =>', to: '(_args) =>' },
    ]
  },
  
  // src/modules/feed/service.ts
  {
    file: 'src/modules/feed/service.ts',
    replacements: [
      { from: '.map((item, index) =>', to: '.map((item, _index) =>' },
      { from: '.forEach((item, index) =>', to: '.forEach((item, _index) =>' },
    ]
  },
  
  // src/modules/images/adminRoutes.ts
  {
    file: 'src/modules/images/adminRoutes.ts',
    replacements: [
      { from: 'imageUploadRequest,', to: '' },
      { from: 'imageUploadResponse,', to: '' },
      { from: 'imageDeleteResponse,', to: '' },
      { from: 'imageSchema,', to: '' },
    ]
  },
  
  // src/modules/images/repository.ts
  {
    file: 'src/modules/images/repository.ts',
    replacements: [
      { from: 'ImageUploadInput,', to: '' },
    ]
  },
  
  // src/modules/images/service.ts
  {
    file: 'src/modules/images/service.ts',
    replacements: [
      { from: 'const invalidChars = /[<>:"/\\\\|?*\\x00-\\x1f]/;', to: 'const invalidChars = /[<>:"/\\\\|?*\\u0000-\\u001f]/;' },
    ]
  },
];

// 执行修复
fixes.forEach(({ file, replacements }) => {
  const filePath = path.join(__dirname, file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${file}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  replacements.forEach(({ from, to }) => {
    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      modified = true;
      console.log(`✅ 修复 ${file}: ${from} -> ${to}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`📝 已保存: ${file}`);
  } else {
    console.log(`ℹ️  无需修改: ${file}`);
  }
});

console.log('\n🎉 Lint修复完成！');
