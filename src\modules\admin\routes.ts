import { OpenAPIHono } from '@hono/zod-openapi';

import { routes as circleRoutes } from '../circle/adminRoutes';
import { routes as eventRoutes } from '../event/adminRoutes';
import { routes as imageRoutes } from '../images/adminRoutes';
import { routes as logRoutes } from '../log/adminRoutes';
import { routes as statsRoutes } from '../stats/adminRoutes';
import { routes as userRoutes } from '../user/adminRoutes';
import { adminVenues } from '../venue/adminRoutes';
import { betterAuthMiddleware } from '@/middlewares/betterAuth';
import { roleGuard } from '@/middlewares/roleGuard';
import { HonoApp } from '@/types';

const routes = new OpenAPIHono<HonoApp>();

routes.use('*', betterAuthMiddleware());

// 不同资源的权限控制
// Events, Circles, Venues, Images: admin + editor 都可以访问
routes.use('/events/*', roleGuard(['admin', 'editor']));
routes.use('/circles/*', roleGuard(['admin', 'editor']));
routes.use('/venues/*', roleGuard(['admin', 'editor']));
routes.use('/images/*', roleGuard(['admin', 'editor']));

// Users, Logs, Stats: 只有 admin 可以访问
routes.use('/users/*', roleGuard('admin'));
routes.use('/logs/*', roleGuard('admin'));
routes.use('/stats/*', roleGuard('admin'));

routes.route('/events', eventRoutes);
routes.route('/circles', circleRoutes);
routes.route('/venues', adminVenues);
routes.route('/images', imageRoutes);
routes.route('/users', userRoutes);
routes.route('/logs', logRoutes);
routes.route('/stats', statsRoutes);

export { routes };
