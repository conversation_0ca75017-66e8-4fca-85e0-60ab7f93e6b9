# 富文本编辑器标签页管理 - 前端扩展文档

> 📝 **版本**: v1.0.0  
> 🕒 **更新时间**: 2025-01-02  
> 👥 **目标读者**: 前端开发者  
> 🎯 **功能**: 为富文本编辑器添加动态标签页管理功能

## 📋 目录

- [概述](#概述)
- [类型定义](#类型定义)
- [API 客户端](#api-客户端)
- [Hooks 实现](#hooks-实现)
- [组件改造](#组件改造)
- [后台管理界面](#后台管理界面)
- [实现步骤](#实现步骤)
- [最佳实践](#最佳实践)

## 概述

### 功能目标

将富文本编辑器的标签页从硬编码改为动态配置，支持：
- 动态获取标签页配置
- 实时更新标签页显示
- 后台管理界面进行 CRUD 操作
- 拖拽排序和启用/禁用功能

### 技术要求

- **向后兼容**: 保持现有组件 API 不变
- **性能优化**: 使用缓存和懒加载
- **用户体验**: 平滑的加载状态和错误处理
- **响应式设计**: 适配不同屏幕尺寸

## 类型定义

```typescript
// src/types/contentType.ts
export interface ContentTypeConfig {
  id: string;
  key: string;                    // 唯一标识符
  label: string;                  // 显示名称
  placeholder?: string;           // 占位符文本
  icon: string;                   // 图标名称 (Lucide 图标)
  sort_order: number;             // 排序顺序
  is_active: boolean;             // 是否启用
  is_system: boolean;             // 是否为系统预设
  created_at?: string;
  updated_at?: string;
}

export interface CreateContentTypeRequest {
  key: string;
  label: string;
  placeholder?: string;
  icon?: string;
  sort_order?: number;
  is_active?: boolean;
}

export interface UpdateContentTypeRequest extends Partial<CreateContentTypeRequest> {}

export interface ReorderContentTypesRequest {
  items: Array<{
    id: string;
    sort_order: number;
  }>;
}

// 扩展现有的 ContentType 类型
export type DynamicContentType = string; // 不再限制为固定的4个值
```

## API 客户端

```typescript
// src/lib/api/contentTypes.ts
import { getCurrentLocale } from '@/lib/locale-utils';
import config from './config';

const API_BASE = `${config.baseUrl}/admin/rich-text/content-types`;

export class ContentTypeAPI {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const locale = getCurrentLocale();
    const response = await fetch(`${API_BASE}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': locale,
        ...options.headers,
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({}));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    const result = await response.json();
    return result.data || result;
  }

  async getAll(): Promise<ContentTypeConfig[]> {
    return this.request<ContentTypeConfig[]>('');
  }

  async getActive(): Promise<ContentTypeConfig[]> {
    const all = await this.getAll();
    return all.filter(type => type.is_active);
  }

  async getById(id: string): Promise<ContentTypeConfig> {
    return this.request<ContentTypeConfig>(`/${id}`);
  }

  async create(data: CreateContentTypeRequest): Promise<ContentTypeConfig> {
    return this.request<ContentTypeConfig>('', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async update(id: string, data: UpdateContentTypeRequest): Promise<ContentTypeConfig> {
    return this.request<ContentTypeConfig>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete(id: string): Promise<void> {
    await this.request<void>(`/${id}`, {
      method: 'DELETE',
    });
  }

  async reorder(items: ReorderContentTypesRequest['items']): Promise<void> {
    await this.request<void>('/reorder', {
      method: 'PUT',
      body: JSON.stringify({ items }),
    });
  }
}

export const contentTypeAPI = new ContentTypeAPI();
```

## Hooks 实现

```typescript
// src/hooks/useContentTypes.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { contentTypeAPI } from '@/lib/api/contentTypes';
import { toast } from 'sonner';
import type { 
  ContentTypeConfig, 
  CreateContentTypeRequest, 
  UpdateContentTypeRequest,
  ReorderContentTypesRequest 
} from '@/types/contentType';

// 查询所有标签页配置
export function useContentTypes() {
  return useQuery({
    queryKey: ['content-types'],
    queryFn: () => contentTypeAPI.getAll(),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 查询启用的标签页配置
export function useActiveContentTypes() {
  return useQuery({
    queryKey: ['content-types', 'active'],
    queryFn: () => contentTypeAPI.getActive(),
    staleTime: 5 * 60 * 1000,
  });
}

// 创建标签页
export function useCreateContentType() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateContentTypeRequest) => contentTypeAPI.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content-types'] });
      toast.success('标签页创建成功');
    },
    onError: (error: Error) => {
      toast.error(`创建失败: ${error.message}`);
    },
  });
}

// 更新标签页
export function useUpdateContentType() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateContentTypeRequest }) =>
      contentTypeAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content-types'] });
      toast.success('标签页更新成功');
    },
    onError: (error: Error) => {
      toast.error(`更新失败: ${error.message}`);
    },
  });
}

// 删除标签页
export function useDeleteContentType() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => contentTypeAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content-types'] });
      toast.success('标签页删除成功');
    },
    onError: (error: Error) => {
      toast.error(`删除失败: ${error.message}`);
    },
  });
}

// 重新排序标签页
export function useReorderContentTypes() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (items: ReorderContentTypesRequest['items']) =>
      contentTypeAPI.reorder(items),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['content-types'] });
      toast.success('排序更新成功');
    },
    onError: (error: Error) => {
      toast.error(`排序更新失败: ${error.message}`);
    },
  });
}

// 获取图标组件的 Hook
export function useContentTypeIcon(iconName: string) {
  const [IconComponent, setIconComponent] = useState<React.ComponentType<any> | null>(null);
  
  useEffect(() => {
    // 动态导入 Lucide 图标
    import('lucide-react')
      .then((lucide) => {
        const Icon = (lucide as any)[iconName] || lucide.FileText;
        setIconComponent(() => Icon);
      })
      .catch(() => {
        // 回退到默认图标
        import('lucide-react').then((lucide) => {
          setIconComponent(() => lucide.FileText);
        });
      });
  }, [iconName]);
  
  return IconComponent;
}
```

## 组件改造

### 1. ContentManager 组件改造

```typescript
// src/components/rich-text-editor/ContentManager.tsx
'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Save, RotateCcw, AlertCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { RichTextEditor } from './RichTextEditor';
import { useRichTextContent, type EntityType } from '@/hooks/useRichTextContent';
import { useActiveContentTypes, useContentTypeIcon } from '@/hooks/useContentTypes';
import { cn } from '@/lib/utils';

interface ContentManagerProps {
  entityType: EntityType;
  entityId: string;
  className?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

// 动态标签页组件
function DynamicTabTrigger({ 
  contentType, 
  isActive, 
  hasContent 
}: { 
  contentType: ContentTypeConfig; 
  isActive: boolean;
  hasContent: boolean;
}) {
  const IconComponent = useContentTypeIcon(contentType.icon);
  
  if (!IconComponent) {
    return (
      <TabsTrigger value={contentType.key} className="relative flex items-center gap-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        {contentType.label}
        {hasContent && (
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full" />
        )}
      </TabsTrigger>
    );
  }
  
  return (
    <TabsTrigger value={contentType.key} className="relative flex items-center gap-2">
      <IconComponent className="h-4 w-4" />
      {contentType.label}
      {hasContent && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full" />
      )}
    </TabsTrigger>
  );
}

export function ContentManager({
  entityType,
  entityId,
  className,
  autoSave = false,
  autoSaveDelay = 2000,
}: ContentManagerProps) {
  const [activeTab, setActiveTab] = useState<string>('');
  const t = useTranslations('richTextEditor');

  // 获取动态标签页配置
  const { 
    data: contentTypes = [], 
    isLoading: isLoadingTypes,
    error: typesError 
  } = useActiveContentTypes();

  // 获取内容数据
  const {
    content,
    updateContentType,
    saveSingle,
    saveAll,
    resetContent,
    hasUnsavedChanges,
    isSaving,
  } = useRichTextContent(entityType, entityId);

  // 设置默认激活标签页
  React.useEffect(() => {
    if (contentTypes.length > 0 && !activeTab) {
      setActiveTab(contentTypes[0].key);
    }
  }, [contentTypes, activeTab]);

  // 加载状态
  if (isLoadingTypes) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载标签页配置中...</span>
        </div>
      </div>
    );
  }

  // 错误状态
  if (typesError) {
    return (
      <div className={cn('space-y-4', className)}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            加载标签页配置失败: {typesError.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 没有可用的标签页
  if (contentTypes.length === 0) {
    return (
      <div className={cn('space-y-4', className)}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            暂无可用的内容类型标签页，请联系管理员配置。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">内容管理</h3>
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-orange-600">
              有未保存的更改
            </Badge>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={saveAll}
            disabled={!hasUnsavedChanges || isSaving}
          >
            <Save className="h-4 w-4 mr-1" />
            {t('buttons.saveAll')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetContent}
            disabled={!hasUnsavedChanges || isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            {t('buttons.reset')}
          </Button>
        </div>
      </div>

      {/* 动态标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className={`grid w-full grid-cols-${Math.min(contentTypes.length, 6)}`}>
          {contentTypes.map((type) => (
            <DynamicTabTrigger
              key={type.key}
              contentType={type}
              isActive={activeTab === type.key}
              hasContent={!!content[type.key]}
            />
          ))}
        </TabsList>

        {contentTypes.map((type) => (
          <TabsContent key={type.key} value={type.key} className="mt-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <DynamicTabTrigger
                      contentType={type}
                      isActive={true}
                      hasContent={!!content[type.key]}
                    />
                    <div>
                      <CardTitle>{type.label}</CardTitle>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => saveSingle(type.key)}
                    disabled={!content[type.key] || isSaving}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    {t('buttons.saveContent', { type: type.label })}
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <RichTextEditor
                  content={content[type.key] || ''}
                  onChange={(value) => updateContentType(type.key, value)}
                  placeholder={type.placeholder || `请输入${type.label}内容...`}
                  autoSave={autoSave}
                  autoSaveDelay={autoSaveDelay}
                  onSave={() => saveSingle(type.key, false)}
                />
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* 状态提示 */}
      {hasUnsavedChanges && !autoSave && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t('status.unsavedWarning')}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
```

## 后台管理界面

### 1. 标签页管理主页面

```typescript
// src/app/admin/rich-text/content-types/page.tsx
'use client';

import React, { useState } from 'react';
import { Plus, Settings, Trash2, GripVertical, Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

import {
  useContentTypes,
  useDeleteContentType,
  useUpdateContentType,
  useReorderContentTypes
} from '@/hooks/useContentTypes';
import { ContentTypeDialog } from '@/components/admin/ContentTypeDialog';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

export default function ContentTypesManagement() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingType, setEditingType] = useState<ContentTypeConfig | null>(null);
  const [deletingType, setDeletingType] = useState<ContentTypeConfig | null>(null);

  const { data: contentTypes = [], isLoading } = useContentTypes();
  const deleteContentType = useDeleteContentType();
  const updateContentType = useUpdateContentType();
  const reorderContentTypes = useReorderContentTypes();

  // 处理拖拽排序
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(contentTypes);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // 更新排序
    const reorderData = items.map((item, index) => ({
      id: item.id,
      sort_order: index + 1,
    }));

    reorderContentTypes.mutate(reorderData);
  };

  // 切换启用状态
  const handleToggleActive = (type: ContentTypeConfig) => {
    updateContentType.mutate({
      id: type.id,
      data: { is_active: !type.is_active },
    });
  };

  // 删除标签页
  const handleDelete = () => {
    if (deletingType) {
      deleteContentType.mutate(deletingType.id, {
        onSuccess: () => setDeletingType(null),
      });
    }
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">加载中...</div>;
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">富文本标签页管理</h1>
          <p className="text-muted-foreground">
            管理富文本编辑器的内容类型标签页配置
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          添加标签页
        </Button>
      </div>

      {/* 标签页列表 */}
      <Card>
        <CardHeader>
          <CardTitle>标签页列表</CardTitle>
        </CardHeader>
        <CardContent>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="content-types">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12"></TableHead>
                        <TableHead>标识符</TableHead>
                        <TableHead>显示名称</TableHead>
                        <TableHead>图标</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>排序</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {contentTypes.map((type, index) => (
                        <Draggable
                          key={type.id}
                          draggableId={type.id}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <TableRow
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={snapshot.isDragging ? 'bg-muted' : ''}
                            >
                              <TableCell>
                                <div
                                  {...provided.dragHandleProps}
                                  className="cursor-grab active:cursor-grabbing"
                                >
                                  <GripVertical className="h-4 w-4 text-muted-foreground" />
                                </div>
                              </TableCell>
                              <TableCell className="font-mono text-sm">
                                {type.key}
                              </TableCell>
                              <TableCell>{type.label}</TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <DynamicIcon iconName={type.icon} />
                                  <span className="text-sm text-muted-foreground">
                                    {type.icon}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <Switch
                                    checked={type.is_active}
                                    onCheckedChange={() => handleToggleActive(type)}
                                    disabled={updateContentType.isPending}
                                  />
                                  {type.is_active ? (
                                    <Eye className="h-4 w-4 text-green-600" />
                                  ) : (
                                    <EyeOff className="h-4 w-4 text-gray-400" />
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                {type.is_system ? (
                                  <Badge variant="secondary">系统预设</Badge>
                                ) : (
                                  <Badge variant="outline">自定义</Badge>
                                )}
                              </TableCell>
                              <TableCell>{type.sort_order}</TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setEditingType(type)}
                                  >
                                    <Settings className="h-4 w-4" />
                                  </Button>
                                  {!type.is_system && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => setDeletingType(type)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </TableBody>
                  </Table>
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </CardContent>
      </Card>

      {/* 创建/编辑对话框 */}
      <ContentTypeDialog
        open={showCreateDialog || !!editingType}
        onOpenChange={(open) => {
          if (!open) {
            setShowCreateDialog(false);
            setEditingType(null);
          }
        }}
        contentType={editingType}
        mode={editingType ? 'edit' : 'create'}
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={!!deletingType} onOpenChange={() => setDeletingType(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除标签页 "{deletingType?.label}" 吗？
              <br />
              <strong>注意：</strong>删除后，该类型的所有内容将无法访问。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// 动态图标组件
function DynamicIcon({ iconName }: { iconName: string }) {
  const IconComponent = useContentTypeIcon(iconName);
  return IconComponent ? <IconComponent className="h-4 w-4" /> : null;
}
```

### 2. 路由配置

```typescript
// src/app/admin/rich-text/layout.tsx
export default function RichTextAdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">富文本管理</h1>
        <p className="text-muted-foreground">
          管理富文本编辑器的配置和内容
        </p>
      </div>
      {children}
    </div>
  );
}
```

### 3. 侧边栏导航更新

```typescript
// src/components/admin/sidebar.tsx (更新)
const baseNav: NavItem[] = [
  { href: "/admin", label: "仪表盘", icon: <LucideLayoutDashboard className="size-4" /> },
  { href: "/admin/events", label: "展会管理", icon: <LucideCalendar className="size-4" /> },
  { href: "/admin/venues", label: "场馆管理", icon: <LucideMapPin className="size-4" /> },
  { href: "/admin/circles", label: "社团管理", icon: <LucideUsers className="size-4" /> },
  { href: "/admin/images", label: "图片管理", icon: <LucideImage className="size-4" /> },
  // 新增富文本管理
  { href: "/admin/rich-text/content-types", label: "富文本标签页", icon: <LucideFileText className="size-4" /> },
];
```

## 实现步骤

### 阶段 1: 基础设施 (1-2 天)

1. **安装依赖**
   ```bash
   pnpm add @hello-pangea/dnd react-hook-form @hookform/resolvers/zod
   ```

2. **创建类型定义**
   ```
   src/types/
   └── contentType.ts
   ```

3. **创建 API 客户端**
   ```
   src/lib/api/
   └── contentTypes.ts
   ```

### 阶段 2: Hooks 和状态管理 (1 天)

1. **创建自定义 Hooks**
   ```
   src/hooks/
   └── useContentTypes.ts
   ```

2. **更新现有 Hooks**
   - 修改 `useRichTextContent` 支持动态内容类型
   - 添加缓存策略

### 阶段 3: 组件改造 (2-3 天)

1. **改造 ContentManager 组件**
   - 从硬编码改为动态获取
   - 添加加载和错误状态
   - 支持动态图标渲染

2. **创建后台管理组件**
   ```
   src/components/admin/
   ├── ContentTypeDialog.tsx
   └── ContentTypesList.tsx
   ```

3. **创建管理页面**
   ```
   src/app/admin/rich-text/
   ├── layout.tsx
   └── content-types/
       └── page.tsx
   ```

### 阶段 4: 集成和测试 (1-2 天)

1. **路由集成**
   - 更新侧边栏导航
   - 添加权限控制

2. **测试和优化**
   - 组件单元测试
   - 集成测试
   - 性能优化

## 最佳实践

### 1. 性能优化

```typescript
// 使用 React.memo 优化重渲染
const DynamicTabTrigger = React.memo(({ contentType, isActive, hasContent }) => {
  // ...
});

// 使用 useMemo 缓存计算结果
const sortedContentTypes = useMemo(() => {
  return contentTypes.sort((a, b) => a.sort_order - b.sort_order);
}, [contentTypes]);

// 使用 useCallback 优化事件处理
const handleTabChange = useCallback((value: string) => {
  setActiveTab(value);
}, []);
```

### 2. 错误处理

```typescript
// 统一的错误边界
function ContentTypeErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            标签页配置加载失败，请刷新页面重试
          </AlertDescription>
        </Alert>
      }
    >
      {children}
    </ErrorBoundary>
  );
}

// 优雅的降级处理
function ContentManagerWithFallback(props: ContentManagerProps) {
  return (
    <ContentTypeErrorBoundary>
      <Suspense fallback={<ContentManagerSkeleton />}>
        <ContentManager {...props} />
      </Suspense>
    </ContentTypeErrorBoundary>
  );
}
```

### 3. 可访问性

```typescript
// 键盘导航支持
const handleKeyDown = (event: React.KeyboardEvent, type: ContentTypeConfig) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    setActiveTab(type.key);
  }
};

// ARIA 标签
<TabsTrigger
  value={type.key}
  aria-label={`切换到${type.label}标签页`}
  onKeyDown={(e) => handleKeyDown(e, type)}
>
  {/* ... */}
</TabsTrigger>
```

### 4. 国际化支持

```typescript
// 支持多语言的标签页配置
interface ContentTypeConfig {
  // ...
  label_i18n?: Record<string, string>; // { "zh": "介绍", "en": "Introduction" }
  placeholder_i18n?: Record<string, string>;
}

// 使用国际化
const getLocalizedLabel = (type: ContentTypeConfig, locale: string) => {
  return type.label_i18n?.[locale] || type.label;
};
```

### 5. 缓存策略

```typescript
// 智能缓存更新
export function useActiveContentTypes() {
  return useQuery({
    queryKey: ['content-types', 'active'],
    queryFn: () => contentTypeAPI.getActive(),
    staleTime: 5 * 60 * 1000, // 5分钟
    cacheTime: 10 * 60 * 1000, // 10分钟
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
}

// 预加载策略
const queryClient = useQueryClient();
useEffect(() => {
  // 预加载标签页配置
  queryClient.prefetchQuery({
    queryKey: ['content-types', 'active'],
    queryFn: () => contentTypeAPI.getActive(),
  });
}, [queryClient]);
```

---

## 📚 相关文档

- [富文本编辑器标签页管理 - 后端扩展文档](./rich-text-tabs-backend-extension.md)
- [富文本模块前端对接文档](../富文本模块前端对接文档.md)
- [React Query 最佳实践](./react-query-best-practices.md)
- [组件设计规范](./component-design-guidelines.md)
