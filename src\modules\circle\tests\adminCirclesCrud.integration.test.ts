import { describe, it, expect } from 'vitest';

import app from '@/app';

// 从 tests/integration/adminCirclesCrud.test.ts 复制并调整相对路径

// @ts-ignore
const Request = globalThis.Request;

interface MockOptions {
  role?: 'admin' | 'editor' | 'viewer';
  uniqueConflict?: boolean;
}

function createMockDB({
  role = 'admin',
  uniqueConflict = false,
}: MockOptions = {}) {
  return {
    prepare: (query: string) => {
      const upper = query.toUpperCase();

      const buildResponse = () => ({
        all: async () => {
          if (upper.includes('FROM AUTH_SESSION')) {
            return {
              results: role ? [{ id: 'u1', username: 'tester', role }] : [],
            };
          }
          return { results: [] };
        },
        first: async () => {
          if (upper.includes('SELECT * FROM CIRCLES WHERE')) {
            return { id: 'c1', name: 'Circle1' };
          }
          return null;
        },
        run: async () => {
          // INSERT 场景触发唯一键冲突
          if (upper.includes('INSERT INTO CIRCLES') && uniqueConflict) {
            throw new Error('UNIQUE constraint failed: circles.name');
          }
          return { success: true };
        },
      });

      return {
        ...buildResponse(),
        bind: (..._args: any[]) => buildResponse(),
      };
    },
  };
}

function fetchWithEnv(req: Request, env: any) {
  return app.fetch(req, env);
}

describe('/admin/circles CRUD', () => {
  const baseHeaders = {
    'Content-Type': 'application/json',
    Cookie: 'auth_session=admin_session',
  };

  it('should create circle successfully and return message/header', async () => {
    const body = { name: 'Circle1' };
    const req = new Request('http://localhost/admin/circles', {
      method: 'POST',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(201);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('社团创建成功')
    );
    const json = (await res.json()) as any;
    expect(json.message).toBe('社团创建成功');
  });

  it('should reject duplicate circle (unique conflict)', async () => {
    const body = { name: 'Circle1' };
    const req = new Request('http://localhost/admin/circles', {
      method: 'POST',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, {
      DB: createMockDB({ uniqueConflict: true }),
    });
    expect(res.status).toBe(409);
  });

  it('should update circle successfully', async () => {
    const body = { name: 'Updated' };
    const req = new Request('http://localhost/admin/circles/c1', {
      method: 'PUT',
      headers: baseHeaders,
      body: JSON.stringify(body),
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(200);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('社团已保存')
    );
    const json = (await res.json()) as any;
    expect(json.message).toBe('社团已保存');
  });

  it('should delete circle successfully', async () => {
    const req = new Request('http://localhost/admin/circles/c1', {
      method: 'DELETE',
      headers: baseHeaders,
    });

    const res = await fetchWithEnv(req, { DB: createMockDB() });
    expect(res.status).toBe(204);
    expect(res.headers.get('X-Success-Message')).toBe(
      encodeURIComponent('社团已删除')
    );
  });
});
