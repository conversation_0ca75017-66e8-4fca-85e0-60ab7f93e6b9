/**
 * 收藏模块骨架屏组件
 * 
 * 提供各种收藏相关组件的加载状态
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

/**
 * 收藏按钮骨架屏
 */
export function BookmarkButtonSkeleton({
  size = 'default',
  variant = 'outline',
  showText = true,
  className,
}: {
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
  showText?: boolean;
  className?: string;
}) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    default: 'h-9 w-9',
    lg: 'h-10 w-10',
  };

  const textSizeClasses = {
    sm: 'w-12',
    default: 'w-16',
    lg: 'w-20',
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Skeleton className={cn(sizeClasses[size], 'rounded-md')} />
      {showText && (
        <Skeleton className={cn('h-4', textSizeClasses[size])} />
      )}
    </div>
  );
}

/**
 * 收藏紧凑按钮骨架屏
 */
export function BookmarkCompactButtonSkeleton({
  className,
}: {
  className?: string;
}) {
  return (
    <Skeleton className={cn('h-8 w-8 rounded-md', className)} />
  );
}

/**
 * 收藏统计骨架屏
 */
export function BookmarkStatsSkeleton({
  variant = 'default',
  showTitle = true,
  className,
}: {
  variant?: 'default' | 'compact';
  showTitle?: boolean;
  className?: string;
}) {
  if (variant === 'compact') {
    return (
      <Card className={cn('', className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-6 w-8" />
            </div>
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('', className)}>
      {showTitle && (
        <CardHeader className="pb-3">
          <Skeleton className="h-6 w-32" />
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        {/* 总数统计 */}
        <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-12" />
          </div>
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>

        {/* 最近活动 */}
        <div className="space-y-3">
          <Skeleton className="h-5 w-24" />
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <Skeleton className="h-8 w-8 rounded-lg" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-20" />
                </div>
                <Skeleton className="h-3 w-16" />
              </div>
            ))}
          </div>
        </div>

        {/* 趋势 */}
        <div className="space-y-3">
          <Skeleton className="h-5 w-16" />
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-6 w-8" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-6 w-8" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 收藏列表项骨架屏
 */
export function BookmarkListItemSkeleton() {
  return (
    <Card className="group hover:shadow-md transition-all duration-200">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Logo */}
          <Skeleton className="w-12 h-12 rounded-lg flex-shrink-0" />
          
          {/* 内容 */}
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-24" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-16 rounded-full" />
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
          </div>
          
          {/* 操作按钮 */}
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 收藏列表骨架屏
 */
export function BookmarkListSkeleton({
  itemCount = 6,
  showHeader = true,
  className,
}: {
  itemCount?: number;
  showHeader?: boolean;
  className?: string;
}) {
  return (
    <div className={cn('space-y-6', className)}>
      {showHeader && (
        <div className="space-y-4">
          {/* 标题 */}
          <Skeleton className="h-8 w-48" />
          
          {/* 搜索和筛选 */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 flex-1" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-24" />
            </div>
          </div>
        </div>
      )}

      {/* 列表项 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: itemCount }).map((_, i) => (
          <BookmarkListItemSkeleton key={i} />
        ))}
      </div>

      {/* 分页 */}
      <div className="flex justify-center">
        <div className="flex gap-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-8" />
          <Skeleton className="h-10 w-8" />
          <Skeleton className="h-10 w-8" />
          <Skeleton className="h-10 w-20" />
        </div>
      </div>
    </div>
  );
}

/**
 * 收藏网格视图骨架屏
 */
export function BookmarkGridSkeleton({
  itemCount = 9,
  className,
}: {
  itemCount?: number;
  className?: string;
}) {
  return (
    <div className={cn('grid gap-4 sm:grid-cols-2 lg:grid-cols-3', className)}>
      {Array.from({ length: itemCount }).map((_, i) => (
        <Card key={i} className="group hover:shadow-md transition-all duration-200">
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* Logo 和标题 */}
              <div className="flex items-start gap-3">
                <Skeleton className="w-10 h-10 rounded-lg flex-shrink-0" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-6 w-6 rounded-md" />
              </div>
              
              {/* 标签 */}
              <div className="flex gap-1.5">
                <Skeleton className="h-5 w-12 rounded-full" />
                <Skeleton className="h-5 w-8 rounded-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * 空状态骨架屏
 */
export function BookmarkEmptySkeleton({
  className,
}: {
  className?: string;
}) {
  return (
    <div className={cn('text-center py-12 space-y-4', className)}>
      <Skeleton className="h-16 w-16 rounded-full mx-auto" />
      <Skeleton className="h-6 w-48 mx-auto" />
      <Skeleton className="h-4 w-64 mx-auto" />
      <Skeleton className="h-10 w-32 mx-auto rounded-md" />
    </div>
  );
}
