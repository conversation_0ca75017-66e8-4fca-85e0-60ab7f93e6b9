import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface FilterBarProps {
  keyword: string
  setKeyword: (keyword: string) => void
}

export default function FilterBar({
  keyword,
  setKeyword,
}: FilterBarProps) {
  return (
    <section className="p-4 flex flex-col gap-3">
      <div className="flex flex-wrap gap-2 items-center">
        <Input
          placeholder="搜索摊位号 / 名称 / 作者"
          value={keyword}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            { setKeyword(e.target.value) }
          }
          className="max-w-xs"
        />
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setKeyword("")
          }}
        >
          重置
        </Button>
      </div>


    </section>
  )
}
