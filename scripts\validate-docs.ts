#!/usr/bin/env tsx

/**
 * 文档链接验证脚本
 * 检查所有 Markdown 文件中的内部链接是否有效
 */

import { readFileSync, existsSync } from 'fs';
import { dirname, resolve } from 'path';
import { glob } from 'fast-glob';

interface LinkIssue {
  file: string;
  line: number;
  link: string;
  issue: string;
}

/**
 * 验证链接是否有效
 */
function validateLink(basePath: string, link: string): boolean {
  // 跳过外部链接
  if (link.startsWith('http://') || link.startsWith('https://')) {
    return true;
  }

  // 跳过锚点链接
  if (link.startsWith('#')) {
    return true;
  }

  // 解析相对路径
  const fullPath = resolve(dirname(basePath), link);
  return existsSync(fullPath);
}

/**
 * 检查单个文件中的链接
 */
function checkFileLinks(filePath: string): LinkIssue[] {
  const issues: LinkIssue[] = [];

  try {
    const content = readFileSync(filePath, 'utf-8');
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // 匹配 Markdown 链接格式 [text](link)
      const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
      let match;

      while ((match = linkRegex.exec(line)) !== null) {
        const link = match[2];

        if (!validateLink(filePath, link)) {
          issues.push({
            file: filePath,
            line: index + 1,
            link,
            issue: 'Link target does not exist',
          });
        }
      }
    });
  } catch (error) {
    issues.push({
      file: filePath,
      line: 0,
      link: '',
      issue: `Failed to read file: ${error}`,
    });
  }

  return issues;
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 Validating documentation links...');

  // 查找所有 Markdown 文件
  const markdownFiles = await glob('**/*.md', {
    ignore: ['node_modules/**', '.git/**', 'coverage/**', '.wrangler/**'],
  });

  console.log(`Found ${markdownFiles.length} Markdown files`);

  let totalIssues = 0;
  const allIssues: LinkIssue[] = [];

  for (const file of markdownFiles) {
    const issues = checkFileLinks(file);
    allIssues.push(...issues);
    totalIssues += issues.length;

    if (issues.length > 0) {
      console.log(`\n❌ ${file}:`);
      issues.forEach((issue) => {
        console.log(`  Line ${issue.line}: ${issue.link} - ${issue.issue}`);
      });
    }
  }

  if (totalIssues === 0) {
    console.log('\n✅ All links are valid!');
  } else {
    console.log(`\n❌ Found ${totalIssues} link issues in ${markdownFiles.length} files`);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
