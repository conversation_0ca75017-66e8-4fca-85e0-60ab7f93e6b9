import type { D1Database, KVNamespace } from '@cloudflare/workers-types';
import type { Context } from 'hono';

// 数据库适配器接口，后续可扩展至其他驱动
export interface DatabaseAdapter {
  execute<T = unknown>(sql: string, params?: unknown[]): Promise<T>;
}

// 基于 Cloudflare D1 的实现
export class D1Adapter implements DatabaseAdapter {
  constructor(private readonly db: D1Database) {}

  async execute<T = unknown>(sql: string, params: unknown[] = []): Promise<T> {
    const stmt = this.db.prepare(sql);
    if (params.length) stmt.bind(...params);
    return (await stmt.all<T>()) as T;
  }
}

// 直接获取 D1 实例
export function getDB(c: Context) {
  return c.env.DB as D1Database;
}

// 直接获取 KV 实例
export function getKV(c: Context): KVNamespace | undefined {
  return (c.env as { CACHE?: KVNamespace } | undefined)?.CACHE;
}

// 返回统一的抽象适配器实例
export function getDBAdapter(c: Context): DatabaseAdapter {
  return new D1Adapter(getDB(c));
}
