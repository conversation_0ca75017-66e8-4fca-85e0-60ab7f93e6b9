# Rich Text Editor

基于 Tiptap 的富文本编辑器组件，为 Ayafeed 项目提供强大的内容管理功能。

## 功能特性

### 文本格式化
- **基础格式**: 粗体、斜体、下划线、删除线、行内代码
- **标题**: H1、H2、H3 标题样式
- **文本对齐**: 左对齐、居中、右对齐
- **文本颜色**: 12种预设颜色选择

### 结构化内容
- **列表**: 无序列表、有序列表
- **引用**: 块引用样式
- **撤销/重做**: 完整的编辑历史管理

### 媒体和链接
- **链接**: 添加和编辑超链接
- **图片**: 插入图片（支持URL）
- **自动保存**: 可配置的自动保存功能

## 组件使用

### RichTextEditor

基础富文本编辑器组件。

```tsx
import { RichTextEditor } from '@/components/rich-text-editor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <RichTextEditor
      content={content}
      onChange={setContent}
      placeholder="开始编写内容..."
      autoSave={false}
      onSave={() => console.log('保存内容')}
    />
  );
}
```

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `content` | `string` | `''` | 编辑器内容（HTML格式） |
| `onChange` | `(content: string) => void` | - | 内容变化回调 |
| `onSave` | `() => void` | - | 保存回调（可选） |
| `placeholder` | `string` | `'Start writing...'` | 占位符文本 |
| `className` | `string` | - | 自定义样式类 |
| `editable` | `boolean` | `true` | 是否可编辑 |
| `autoSave` | `boolean` | `false` | 是否启用自动保存 |
| `autoSaveDelay` | `number` | `2000` | 自动保存延迟（毫秒） |

### ContentManager

完整的内容管理界面，支持多种内容类型。

```tsx
import { ContentManager } from '@/components/rich-text-editor';

function EventEditPage() {
  return (
    <ContentManager
      entityType="event"
      entityId="event-123"
      autoSave={true}
      autoSaveDelay={3000}
    />
  );
}
```

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `entityType` | `'event' \| 'venue' \| 'circle'` | - | 实体类型 |
| `entityId` | `string` | - | 实体ID |
| `className` | `string` | - | 自定义样式类 |
| `autoSave` | `boolean` | `false` | 是否启用自动保存 |
| `autoSaveDelay` | `number` | `2000` | 自动保存延迟（毫秒） |

### useRichTextContent Hook

用于管理富文本内容的 React Hook。

```tsx
import { useRichTextContent } from '@/hooks/useRichTextContent';

function MyComponent() {
  const {
    content,
    updateContentType,
    saveAll,
    saveSingle,
    isLoading,
    isSaving,
    hasUnsavedChanges,
  } = useRichTextContent({
    entityType: 'event',
    entityId: 'event-123',
    autoSave: false,
  });

  return (
    <div>
      <RichTextEditor
        content={content.introduction || ''}
        onChange={(value) => updateContentType('introduction', value)}
        onSave={() => saveSingle('introduction')}
      />
      {hasUnsavedChanges && (
        <button onClick={saveAll}>保存所有更改</button>
      )}
    </div>
  );
}
```

### useRichTextContentType Hook

用于管理单个内容类型的简化 Hook。

```tsx
import { useRichTextContentType } from '@/hooks/useRichTextContent';

function IntroductionEditor() {
  const {
    value,
    setValue,
    save,
    isLoading,
    isSaving,
    hasUnsavedChanges,
  } = useRichTextContentType('event', 'event-123', 'introduction');

  return (
    <RichTextEditor
      content={value}
      onChange={setValue}
      onSave={save}
      autoSave={false}
    />
  );
}
```

## 内容类型

系统支持四种内容类型：

1. **Introduction** (introduction): 基本信息和概述
2. **Highlights** (highlights): 重点特色和亮点
3. **Guide** (guide): 指南和说明信息
4. **Notices** (notices): 重要公告和更新

## 样式定制

富文本编辑器使用 Tailwind CSS 进行样式设计，支持深色模式。可以通过以下方式定制样式：

1. **全局样式**: 修改 `src/styles/rich-text-editor.css`
2. **组件样式**: 通过 `className` prop 传递自定义样式
3. **主题定制**: 通过 CSS 变量定制颜色主题

## 安全性

- 使用 `isomorphic-dompurify` 进行 HTML 内容清理
- 防止 XSS 攻击
- 服务端和客户端双重验证

## 演示页面

访问 `/rich-text-demo` 查看完整的功能演示和使用示例。

## 注意事项

1. **依赖要求**: 需要安装所有 Tiptap 相关依赖
2. **样式导入**: 确保在 `globals.css` 中导入了富文本编辑器样式
3. **查询客户端**: 组件需要在 React Query 提供者内使用
4. **自动保存**: 启用自动保存时注意性能影响
5. **图片上传**: 当前版本仅支持 URL 图片，未来可集成文件上传功能
