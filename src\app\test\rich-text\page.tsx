'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { RichTextEditor } from '@/components/rich-text-editor';
import { useRichTextContent, type EntityType, type ContentType } from '@/hooks/useRichTextContent';
import { Save, Clock, RefreshCw, Trash2 } from 'lucide-react';
import { toast } from 'sonner';

const contentTypes: { key: ContentType; label: string; description: string }[] = [
  {
    key: 'introduction',
    label: '介绍',
    description: '基本介绍信息',
  },
  {
    key: 'highlights',
    label: '亮点',
    description: '重点特色内容',
  },
  {
    key: 'guide',
    label: '指南',
    description: '使用指南或说明',
  },
  {
    key: 'notices',
    label: '公告',
    description: '重要通知信息',
  },
];

export default function RichTextTestPage() {
  const [entityType, setEntityType] = useState<EntityType>('event');
  const [entityId, setEntityId] = useState('test-event-001');
  const [activeTab, setActiveTab] = useState<ContentType>('introduction');

  const {
    content,
    serverContent,
    isLoading,
    error,
    hasUnsavedChanges,
    updateContentType,
    saveAll,
    saveSingle,
    reset,
    refetch,
    isSaving,
    saveError,
  } = useRichTextContent({
    entityType,
    entityId,
    autoSave: false, // 手动保存模式
  });

  const handleEntityChange = () => {
    // 重新获取数据
    refetch();
  };

  const handleSaveAll = async () => {
    try {
      await saveAll();
      toast.success('All content saved successfully!');
    } catch (error) {
      toast.error('Failed to save content');
    }
  };

  const handleSaveCurrent = async () => {
    try {
      await saveSingle(activeTab);
      toast.success(`${contentTypes.find(t => t.key === activeTab)?.label} saved successfully!`);
    } catch (error) {
      toast.error('Failed to save content');
    }
  };

  const handleReset = () => {
    reset();
    toast.info('Content reset to server version');
  };

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Content</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-500 mb-4">{error.message}</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold">Rich Text Editor Test</h1>
        <p className="text-muted-foreground mt-2">
          Test the rich text editor with real API integration
        </p>
      </div>

      {/* 实体配置 */}
      <Card>
        <CardHeader>
          <CardTitle>Entity Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="entityType">Entity Type</Label>
              <select
                id="entityType"
                value={entityType}
                onChange={(e) => setEntityType(e.target.value as EntityType)}
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="event">Event</option>
                <option value="venue">Venue</option>
                <option value="circle">Circle</option>
              </select>
            </div>
            <div>
              <Label htmlFor="entityId">Entity ID</Label>
              <Input
                id="entityId"
                value={entityId}
                onChange={(e) => setEntityId(e.target.value)}
                placeholder="Enter entity ID"
              />
            </div>
            <div className="flex items-end">
              <Button onClick={handleEntityChange} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Load Content
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 状态指示器 */}
      <div className="flex items-center gap-4">
        {isLoading && (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Loading...
          </Badge>
        )}
        {isSaving && (
          <Badge variant="secondary">
            <Save className="h-3 w-3 mr-1" />
            Saving...
          </Badge>
        )}
        {hasUnsavedChanges && (
          <Badge variant="destructive">
            Unsaved Changes
          </Badge>
        )}
        {saveError && (
          <Badge variant="destructive">
            Save Error: {saveError.message}
          </Badge>
        )}
      </div>

      {/* 富文本编辑器 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Content Editor</CardTitle>
            <div className="flex gap-2">
              <Button
                onClick={handleSaveCurrent}
                disabled={isSaving || !hasUnsavedChanges}
                size="sm"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Current
              </Button>
              <Button
                onClick={handleSaveAll}
                disabled={isSaving || !hasUnsavedChanges}
                size="sm"
                variant="default"
              >
                <Save className="h-4 w-4 mr-2" />
                Save All
              </Button>
              <Button
                onClick={handleReset}
                disabled={isSaving || !hasUnsavedChanges}
                size="sm"
                variant="outline"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ContentType)}>
            <TabsList className="grid w-full grid-cols-4">
              {contentTypes.map((type) => (
                <TabsTrigger key={type.key} value={type.key}>
                  {type.label}
                  {content[type.key] && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {content[type.key]?.length || 0}
                    </Badge>
                  )}
                </TabsTrigger>
              ))}
            </TabsList>

            {contentTypes.map((type) => (
              <TabsContent key={type.key} value={type.key} className="mt-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">{type.label}</h3>
                    <p className="text-sm text-muted-foreground">{type.description}</p>
                  </div>

                  <RichTextEditor
                    content={content[type.key] || ''}
                    onChange={(value) => updateContentType(type.key, value)}
                    placeholder={`Enter ${type.label.toLowerCase()}...`}
                    className="min-h-[400px]"
                  />

                  {/* 显示服务器内容对比 */}
                  {serverContent[type.key] && serverContent[type.key] !== content[type.key] && (
                    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                      <h4 className="font-medium text-yellow-800 mb-2">Server Content (for comparison):</h4>
                      <div 
                        className="text-sm text-yellow-700 prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: serverContent[type.key] || '' }}
                      />
                    </div>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>

      {/* 调试信息 */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div><strong>Entity:</strong> {entityType}/{entityId}</div>
            <div><strong>Active Tab:</strong> {activeTab}</div>
            <div><strong>Has Unsaved Changes:</strong> {hasUnsavedChanges ? 'Yes' : 'No'}</div>
            <div><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</div>
            <div><strong>Is Saving:</strong> {isSaving ? 'Yes' : 'No'}</div>
            <div><strong>Content Keys:</strong> {Object.keys(content).join(', ') || 'None'}</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
