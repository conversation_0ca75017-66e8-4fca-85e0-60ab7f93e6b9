"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";

import MultilingualEventForm from "@/components/admin/MultilingualEventForm";
import { useZodForm } from "@/hooks";
import { useAdminEventDetail } from "@/hooks/admin/useAdminEventDetail";
import { useUpdateEvent } from "@/hooks/admin/useUpdateEvent";

import { MultilingualEventInputSchema, type MultilingualEventInput } from "@/schemas/event";

export default function EditEventPage() {
  const { id } = useParams<{ id: string }>();
  const { multilingualData, isLoading: detailLoading } = useAdminEventDetail(id);
  const updateEvent = useUpdateEvent(id);

  const form = useZodForm(MultilingualEventInputSchema, {
    defaultValues: {
      id: id ?? "",
      name_en: "",
      name_ja: "",
      name_zh: "",
      date_en: "",
      date_ja: "",
      date_zh: "",
      image_url: "",
      venue_id: "",
      url: "",
    } as Partial<MultilingualEventInput>,
    mode: "onBlur",
  });

  // 接口返回数据后填充表单
  useEffect(() => {
    if (multilingualData) {
      // 确保所有字段都有默认值
      const formData: MultilingualEventInput = {
        id: multilingualData.id,
        name_en: multilingualData.name_en,
        name_ja: multilingualData.name_ja,
        name_zh: multilingualData.name_zh,
        date_en: multilingualData.date_en,
        date_ja: multilingualData.date_ja,
        date_zh: multilingualData.date_zh,
        date_sort: multilingualData.date_sort,
        image_url: multilingualData.image_url || "",
        venue_id: (multilingualData as any).venue_id || "",
        url: multilingualData.url || "",
      };
      // 使用 reset 并设置 keepDirty: false 来避免触发 dirty 状态
      form.reset(formData, { keepDirty: false });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [multilingualData]); // form.reset 是稳定的方法，multilingualData 通过 useMemo 稳定化

  function onSubmit(values: MultilingualEventInput) {
    updateEvent.mutate(values, {
      showSuccessToast: true,
      allowEventUpdate: true,
    });
  }

  if (detailLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <p className="text-lg text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (!multilingualData) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <p className="text-lg text-destructive">未找到展会信息</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">编辑展会</h1>
        <p className="text-muted-foreground mt-2">
          修改三种语言的展会信息，确保国际化支持
        </p>
      </div>

      <MultilingualEventForm
        form={form}
        onSubmit={onSubmit}
        isSubmitting={updateEvent.isPending}
        submitText="更新展会"
        title="编辑展会信息"
        description="修改中文、日文、英文三种语言的展会信息"
        eventId={id}
      />
    </div>
  );
}