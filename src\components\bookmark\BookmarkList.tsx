'use client';

import React, { useState, useMemo } from 'react';
import { Search, Filter, SortAsc, SortDesc, Calendar, Grid, List } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useBookmarks } from '@/hooks/useBookmark';
import { BookmarkButton } from './BookmarkButton';
import { useDebounce } from '@/hooks';
import { BookmarkListSkeleton, BookmarkGridSkeleton, BookmarkEmptySkeleton } from './BookmarkSkeleton';
import Link from 'next/link';
import Image from 'next/image';

interface BookmarkListProps {
  className?: string;
  showHeader?: boolean;
  pageSize?: number;
}

type SortOption = 'created_at_desc' | 'created_at_asc' | 'name_asc' | 'name_desc';
type ViewMode = 'grid' | 'list';

/**
 * BookmarkList 收藏列表组件
 * 
 * 功能特性：
 * - 搜索收藏的社团
 * - 排序（按时间、名称）
 * - 分页加载
 * - 网格/列表视图切换
 * - 响应式设计
 */
export function BookmarkList({
  className,
  showHeader = true,
  pageSize = 20,
}: BookmarkListProps) {
  // 搜索和筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('created_at_desc');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [page, setPage] = useState(1);

  // 防抖搜索
  const debouncedSearch = useDebounce(searchKeyword, 300);

  // 构建查询参数
  const queryParams = useMemo(() => ({
    page: page.toString(),
    pageSize: pageSize.toString(),
    search: debouncedSearch || undefined,
    sortBy: sortBy,
  }), [page, pageSize, debouncedSearch, sortBy]);

  // 获取收藏数据
  const {
    data: bookmarksData,
    isLoading,
    error,
    refetch,
  } = useBookmarks(queryParams);

  const bookmarks = bookmarksData?.data?.items || [];
  const total = bookmarksData?.data?.total || 0;
  const hasMore = page * pageSize < total;

  // 处理搜索
  const handleSearchChange = (value: string) => {
    setSearchKeyword(value);
    setPage(1); // 重置页码
  };

  // 处理排序
  const handleSortChange = (value: SortOption) => {
    setSortBy(value);
    setPage(1); // 重置页码
  };

  // 加载更多
  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  // 重置筛选
  const handleReset = () => {
    setSearchKeyword('');
    setSortBy('created_at_desc');
    setPage(1);
  };

  if (error) {
    return (
      <div className={cn('text-center py-8', className)}>
        <p className="text-muted-foreground mb-4">Failed to load bookmarks</p>
        <Button onClick={() => refetch()} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* 页面头部 */}
      {showHeader && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">My Bookmarks</h1>
              <p className="text-muted-foreground">
                {total > 0 ? `${total} bookmarked circles` : 'No bookmarks yet'}
              </p>
            </div>
            
            {/* 视图切换 */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* 搜索和筛选栏 */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search bookmarked circles..."
                value={searchKeyword}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* 排序选择 */}
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at_desc">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Newest First
                  </div>
                </SelectItem>
                <SelectItem value="created_at_asc">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Oldest First
                  </div>
                </SelectItem>
                <SelectItem value="name_asc">
                  <div className="flex items-center gap-2">
                    <SortAsc className="w-4 h-4" />
                    Name A-Z
                  </div>
                </SelectItem>
                <SelectItem value="name_desc">
                  <div className="flex items-center gap-2">
                    <SortDesc className="w-4 h-4" />
                    Name Z-A
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {/* 重置按钮 */}
            {(searchKeyword || sortBy !== 'created_at_desc') && (
              <Button variant="outline" onClick={handleReset}>
                Reset
              </Button>
            )}
          </div>
        </div>
      )}

      {/* 内容区域 */}
      {isLoading && page === 1 ? (
        viewMode === 'grid' ? (
          <BookmarkGridSkeleton itemCount={pageSize} />
        ) : (
          <BookmarkListSkeleton itemCount={pageSize} showHeader={false} />
        )
      ) : bookmarks.length === 0 ? (
        <BookmarkEmptyState
          hasSearch={!!debouncedSearch}
          onReset={handleReset}
        />
      ) : (
        <>
          {/* 书签列表 */}
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'space-y-4'
          )}>
            {bookmarks.map((bookmark: any) => (
              <BookmarkCard
                key={bookmark.id}
                bookmark={bookmark}
                viewMode={viewMode}
              />
            ))}
          </div>

          {/* 加载更多 */}
          {hasMore && (
            <div className="flex justify-center">
              <Button
                onClick={handleLoadMore}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}

/**
 * 单个收藏卡片组件
 */
function BookmarkCard({ 
  bookmark, 
  viewMode 
}: { 
  bookmark: any; 
  viewMode: ViewMode;
}) {
  const circle = bookmark.circle;
  
  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <Link href={`/circles/${circle.id}`} className="flex-shrink-0">
              <Image
                src={circle.logoUrl || '/images/circles/placeholder.svg'}
                alt={circle.name}
                width={48}
                height={48}
                className="w-12 h-12 rounded-lg object-cover"
              />
            </Link>
            
            <div className="flex-1 min-w-0">
              <Link href={`/circles/${circle.id}`}>
                <h3 className="font-medium hover:text-primary transition-colors truncate">
                  {circle.name}
                </h3>
              </Link>
              {circle.author && (
                <p className="text-sm text-muted-foreground truncate">
                  by {circle.author}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                Bookmarked {new Date(bookmark.createdAt).toLocaleDateString()}
              </p>
            </div>
            
            <BookmarkButton
              circleId={circle.id}
              size="sm"
              variant="ghost"
              showText={false}
              knownStatus={{
                isBookmarked: true,
                bookmarkId: bookmark.id,
              }}
            />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <Link href={`/circles/${circle.id}`} className="flex-shrink-0">
            <Image
              src={circle.logoUrl || '/images/circles/placeholder.svg'}
              alt={circle.name}
              width={64}
              height={64}
              className="w-16 h-16 rounded-lg object-cover"
            />
          </Link>
          <BookmarkButton
            circleId={circle.id}
            size="sm"
            variant="ghost"
            showText={false}
            knownStatus={{
              isBookmarked: true,
              bookmarkId: bookmark.id,
            }}
          />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Link href={`/circles/${circle.id}`}>
          <CardTitle className="text-base hover:text-primary transition-colors line-clamp-2">
            {circle.name}
          </CardTitle>
        </Link>
        {circle.author && (
          <p className="text-sm text-muted-foreground mt-1 truncate">
            by {circle.author}
          </p>
        )}
        <div className="flex items-center justify-between mt-3">
          <Badge variant="secondary" className="text-xs">
            {new Date(bookmark.createdAt).toLocaleDateString()}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 空状态组件
 */
function BookmarkEmptyState({ 
  hasSearch, 
  onReset 
}: { 
  hasSearch: boolean; 
  onReset: () => void;
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
        <Search className="w-8 h-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium mb-2">
        {hasSearch ? 'No matching bookmarks' : 'No bookmarks yet'}
      </h3>
      <p className="text-muted-foreground mb-4">
        {hasSearch 
          ? 'Try adjusting your search terms'
          : 'Start bookmarking circles to see them here'
        }
      </p>
      {hasSearch && (
        <Button onClick={onReset} variant="outline">
          Clear Search
        </Button>
      )}
    </div>
  );
}

/**
 * 骨架屏组件
 */
function BookmarkListSkeleton({ viewMode }: { viewMode: ViewMode }) {
  const skeletonCount = 8;
  
  return (
    <div className={cn(
      viewMode === 'grid' 
        ? 'grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
        : 'space-y-4'
    )}>
      {Array.from({ length: skeletonCount }).map((_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-4">
            <div className={cn(
              'flex gap-4',
              viewMode === 'grid' ? 'flex-col' : 'items-center'
            )}>
              <div className={cn(
                'bg-muted rounded-lg',
                viewMode === 'grid' ? 'w-16 h-16' : 'w-12 h-12 flex-shrink-0'
              )} />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-3 bg-muted rounded w-1/2" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default BookmarkList;
