# 前端开发文档

> 🚀 **快速上手指南** - Ayafeed 前端开发的完整文档

## 📋 文档目录

| 文档 | 说明 | 重要程度 |
|------|------|----------|
| [快速开始](./quick-start.md) | 项目设置和开发环境 | ⭐⭐⭐⭐⭐ |
| [API客户端生成](./client-generation.md) | 类型安全的API客户端 | ⭐⭐⭐⭐⭐ |
| [认证集成](./authentication.md) | 用户登录和权限验证 | ⭐⭐⭐⭐ |
| [多语言集成](./i18n-integration.md) | 国际化最佳实践 | ⭐⭐⭐⭐ |
| [错误处理](./error-handling.md) | 统一错误处理方案 | ⭐⭐⭐ |
| [常用示例](./common-examples.md) | 复制粘贴即用的代码 | ⭐⭐⭐ |
| [业务流程](./business-flows.md) | 核心业务流程说明 | ⭐⭐ |

## 🎯 核心资源

### OpenAPI 规范文件
```
openapi.json
```
**用途**: 自动生成前端API客户端代码

### 环境配置
```bash
# 开发环境
NEXT_PUBLIC_API_URL=http://localhost:8787

# 生产环境
NEXT_PUBLIC_API_URL=https://api.ayafeed.com
```

## 🚀 快速开始

### 1. 安装依赖
```bash
# 克隆项目
git clone https://github.com/your-username/ayafeed.git
cd ayafeed

# 安装依赖
pnpm install
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp env.example .env.local

# 编辑环境变量
# NEXT_PUBLIC_API_URL=http://localhost:8787
```

### 3. 生成API客户端
```bash
# 生成API类型定义和客户端代码
pnpm gen:api
pnpm gen:rq
```

### 4. 启动开发服务器
```bash
# 启动开发服务器
pnpm dev

# 访问 http://localhost:3000
```

### 5. 使用示例
```typescript
// 使用自动生成的 React Query hooks
import { useEvents } from '@/hooks/useEvents';

function EventList() {
  const { data: events, isLoading, error } = useEvents({
    page: 1,
    pageSize: 10
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败</div>;

  return (
    <div>
      {events?.items.map(event => (
        <EventCard key={event.id} event={event} />
      ))}
    </div>
  );
}
```

## 📞 技术支持

### 联系方式
- **GitHub Issues**: [提交问题](https://github.com/ayafeed/ayafeed-api/issues)
- **API文档**: [完整文档](../api/)
- **更新通知**: 关注仓库获取API变更通知

### 常见问题
1. **API变更如何获知?** - 订阅仓库通知，关注 [CHANGELOG](../changelog/CHANGELOG.md)
2. **如何处理认证?** - 查看 [认证集成指南](./authentication.md)
3. **多语言如何实现?** - 参考 [多语言集成指南](./i18n-integration.md)

## 🔄 更新机制

### API版本管理
- **当前版本**: v0.4.2.5
- **版本策略**: 语义化版本控制
- **破坏性变更**: 会在主版本号中体现

### 同步流程
1. 后端更新API后会更新 `openapi.json`
2. 前端重新生成客户端代码
3. 更新相关的业务逻辑代码
4. 测试验证功能正常

---

**下一步**: 阅读 [快速开始指南](./quick-start.md) 开始集成 🚀
