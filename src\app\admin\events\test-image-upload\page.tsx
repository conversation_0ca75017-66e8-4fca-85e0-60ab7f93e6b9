"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { EventImageUpload } from "@/components/events/EventImageUpload";

export default function TestImageUploadPage() {
  const [testEventId] = useState("test-event-" + Date.now());

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* 页面头部 */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">事件图片上传测试</h1>
        <p className="text-muted-foreground">
          测试改进后的事件图片上传功能，包括图片预览、替换和多变体支持。
        </p>
      </div>

      {/* 测试说明 */}
      <Card>
        <CardHeader>
          <CardTitle>功能改进说明</CardTitle>
          <CardDescription>
            本次改进针对 admin/events/[id]/edit 页面的图片上传功能
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2 text-green-600">✅ 新增功能</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 使用 admin/images 风格的图片展示</li>
                <li>• 支持图片预览和大图查看</li>
                <li>• 显示多个变体信息</li>
                <li>• hover 层显示操作按钮</li>
                <li>• 替换按钮代替删除按钮</li>
                <li>• 支持键盘导航预览</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2 text-blue-600">🔧 技术改进</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• EventImageCard 组件</li>
                <li>• ImagePreview 集成</li>
                <li>• 替换模式状态管理</li>
                <li>• 响应式网格布局</li>
                <li>• 变体信息显示</li>
                <li>• 更好的错误处理</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 图片上传测试 */}
      <Card>
        <CardHeader>
          <CardTitle>图片上传组件测试</CardTitle>
          <CardDescription>
            测试事件ID: {testEventId}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EventImageUpload
            eventId={testEventId}
            onUploadSuccess={(images) => {
              console.log('上传成功:', images);
            }}
            onUploadError={(error) => {
              console.error('上传失败:', error);
            }}
          />
        </CardContent>
      </Card>

      {/* 使用指南 */}
      <Card>
        <CardHeader>
          <CardTitle>使用指南</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">上传新图片</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>拖拽图片文件到上传区域</li>
                <li>或点击上传区域选择文件</li>
                <li>预览选中的图片</li>
                <li>点击"开始上传"按钮</li>
                <li>等待处理和上传完成</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium mb-2">管理已上传图片</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>hover 图片查看操作按钮</li>
                <li>点击眼睛图标预览大图</li>
                <li>点击复制图标复制链接</li>
                <li>点击下载图标下载图片</li>
                <li>点击替换图标替换图片</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium mb-2">图片预览功能</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>点击图片或眼睛图标打开预览</li>
                <li>使用左右箭头或键盘导航</li>
                <li>查看不同变体的图片</li>
                <li>ESC 键或点击关闭按钮退出</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium mb-2">替换图片功能</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>hover 图片点击橙色替换按钮</li>
                <li>自动打开文件选择对话框</li>
                <li>选择新图片文件</li>
                <li>自动开始替换流程</li>
                <li>旧图片会被新图片替换</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 技术细节 */}
      <Card>
        <CardHeader>
          <CardTitle>技术实现细节</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground space-y-2">
            <p><strong>组件架构:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>EventImageUpload: 主要上传组件</li>
              <li>EventImageCard: 专门的事件图片卡片</li>
              <li>ImagePreview: 图片预览组件</li>
            </ul>
            
            <p><strong>状态管理:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>isReplaceMode: 替换模式状态</li>
              <li>replaceGroupId: 要替换的图片组ID</li>
              <li>previewState: 预览状态管理</li>
            </ul>
            
            <p><strong>API 集成:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>useBatchEventImageUpload: 批量上传hook</li>
              <li>useEventImage: 获取已上传图片</li>
              <li>自动变体生成和处理</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
