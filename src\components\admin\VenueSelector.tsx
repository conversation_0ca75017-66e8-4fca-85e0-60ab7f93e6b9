"use client";

import { useState } from "react";
import { Check, ChevronsUpDown, MapPin, Plus } from "lucide-react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useAdminVenues } from "@/hooks/admin/useVenues";

interface VenueSelectorProps {
  value?: string;
  onValueChange: (venueId: string) => void;
  placeholder?: string;
  className?: string;
}

export default function VenueSelector({
  value,
  onValueChange,
  placeholder = "选择场馆...",
  className
}: VenueSelectorProps) {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");

  // 获取venues列表
  const { data: venuesData, isLoading } = useAdminVenues({
    keyword: searchKeyword || undefined,
    pageSize: "50", // 获取更多选项
  });

  const venues = venuesData?.items || [];
  const selectedVenue = venues.find((venue) => venue.id === value);

  const handleSelect = (venueId: string) => {
    onValueChange(venueId);
    setOpen(false);
  };

  const handleCreateNew = () => {
    setOpen(false);
    router.push("/admin/venues/new");
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedVenue ? (
              <div className="flex items-center gap-2 truncate">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="truncate">
                  {selectedVenue.name_zh || selectedVenue.name_ja || selectedVenue.name_en}
                </span>
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="搜索场馆..."
              value={searchKeyword}
              onValueChange={setSearchKeyword}
            />
            <CommandEmpty>
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground mb-3">
                  没有找到匹配的场馆
                </p>
                <Button
                  size="sm"
                  onClick={handleCreateNew}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  创建新场馆
                </Button>
              </div>
            </CommandEmpty>
            <CommandGroup>
              {venues.map((venue) => (
                <CommandItem
                  key={venue.id}
                  value={venue.id}
                  onSelect={() => handleSelect(venue.id)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">
                        {venue.name_zh || venue.name_ja || venue.name_en}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        {venue.address_zh || venue.address_ja || venue.address_en || "地址未设置"}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {venue.capacity && (
                      <Badge variant="outline" className="text-xs">
                        {venue.capacity.toLocaleString()}人
                      </Badge>
                    )}
                    <Check
                      className={cn(
                        "h-4 w-4",
                        value === venue.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
            {venues.length > 0 && (
              <div className="border-t p-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCreateNew}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  创建新场馆
                </Button>
              </div>
            )}
          </Command>
        </PopoverContent>
      </Popover>

      {/* 显示选中场馆的详细信息 */}
      {selectedVenue && (
        <div className="p-3 bg-muted/50 rounded-lg border">
          <div className="flex items-start justify-between">
            <div className="space-y-1 flex-1">
              <div className="font-medium">
                {selectedVenue.name_zh || selectedVenue.name_ja || selectedVenue.name_en}
              </div>
              {(selectedVenue.address_zh || selectedVenue.address_ja || selectedVenue.address_en) && (
                <div className="text-sm text-muted-foreground">
                  📍 {selectedVenue.address_zh || selectedVenue.address_ja || selectedVenue.address_en}
                </div>
              )}
              <div className="text-xs text-muted-foreground">
                📍 {selectedVenue.lat.toFixed(4)}, {selectedVenue.lng.toFixed(4)}
                {selectedVenue.capacity && (
                  <span className="ml-2">
                    👥 {selectedVenue.capacity.toLocaleString()} 人
                  </span>
                )}
              </div>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => router.push(`/admin/venues/${selectedVenue.id}/edit`)}
            >
              编辑
            </Button>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="text-sm text-muted-foreground">
          加载场馆列表...
        </div>
      )}
    </div>
  );
}
