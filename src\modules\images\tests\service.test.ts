import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { D1Database, R2Bucket } from '@cloudflare/workers-types';

import { ImageServiceImpl } from '../service';
import type { ImageUploadInput } from '../schema';

// Mock dependencies
const mockDB = {
  prepare: vi.fn(),
} as unknown as D1Database;

const mockR2 = {
  put: vi.fn(),
  delete: vi.fn(),
} as unknown as R2Bucket;

const mockRepository = {
  create: vi.fn(),
  findByResource: vi.fn(),
  findBatchByEvents: vi.fn(),
  deleteByPaths: vi.fn(),
};

describe('ImageService', () => {
  let imageService: ImageServiceImpl;

  beforeEach(() => {
    vi.clearAllMocks();
    imageService = new ImageServiceImpl(mockDB, mockR2);
    // Mock repository
    (imageService as any).repository = mockRepository;
  });

  describe('generateImagePath', () => {
    it('should generate correct path for original variant', () => {
      const path = imageService.generateImagePath(
        'event',
        'test-event-id',
        'poster',
        'original',
        'jpg'
      );

      expect(path).toBe('/images/events/test-event-id/poster.jpg');
    });

    it('should generate correct path for non-original variant', () => {
      const path = imageService.generateImagePath(
        'circle',
        'test-circle-id',
        'logo',
        'thumb',
        'png'
      );

      expect(path).toBe('/images/circles/test-circle-id/logo_thumb.png');
    });
  });

  describe('uploadImage', () => {
    it('should upload image successfully', async () => {
      // Mock file - create a file with enough size to pass validation (>100 bytes)
      const mockFile = new File(['test'.repeat(50)], 'test.jpg', {
        type: 'image/jpeg',
      });

      const metadata: ImageUploadInput = {
        category: 'event',
        resourceId: 'test-event-id',
        imageType: 'poster',
        variant: 'thumb',
      };

      // Mock repository response
      mockRepository.create.mockResolvedValue({
        id: 'test-image-id',
        group_id: 'test-group-id',
        file_path: '/images/events/test-event-id/poster_thumb.jpeg',
        variant: 'thumb',
      });

      // Mock R2 put
      (mockR2.put as any).mockResolvedValue(undefined);

      const result = await imageService.uploadImage(mockFile, metadata);

      expect(result).toEqual({
        id: 'test-image-id',
        groupId: 'test-group-id',
        relativePath: '/images/events/test-event-id/poster_thumb.jpeg',
        variant: 'thumb',
        metadata: {
          size: 200, // 'test'.repeat(50) file size
          dimensions: {
            width: expect.any(Number),
            height: expect.any(Number),
          },
          format: 'jpeg',
        },
      });

      expect(mockR2.put).toHaveBeenCalledWith(
        'images/events/test-event-id/poster_thumb.jpeg',
        expect.any(ArrayBuffer),
        {
          httpMetadata: {
            contentType: 'image/jpeg',
          },
        }
      );
    });

    it('should throw error for invalid file type', async () => {
      // Create a file with enough size to pass size validation but wrong type
      const mockFile = new File(['test'.repeat(50)], 'test.txt', {
        type: 'text/plain',
      });

      const metadata: ImageUploadInput = {
        category: 'event',
        resourceId: 'test-event-id',
        imageType: 'poster',
        variant: 'thumb',
      };

      await expect(
        imageService.uploadImage(mockFile, metadata)
      ).rejects.toThrow('不支持的文件类型');
    });

    it('should throw error for file too large', async () => {
      // Create a mock file that's too large
      const mockFile = {
        size: 6 * 1024 * 1024, // 6MB
        type: 'image/jpeg',
        name: 'large.jpg',
        stream: () => new ReadableStream(),
      } as File;

      const metadata: ImageUploadInput = {
        category: 'event',
        resourceId: 'test-event-id',
        imageType: 'poster',
        variant: 'thumb',
      };

      await expect(
        imageService.uploadImage(mockFile, metadata)
      ).rejects.toThrow('文件大小超过限制');
    });
  });

  describe('getImagesByResource', () => {
    it('should return images with pagination', async () => {
      const mockImages = [
        { id: '1', variant: 'thumb' },
        { id: '2', variant: 'medium' },
      ];

      mockRepository.findByResource.mockResolvedValueOnce(mockImages);
      mockRepository.findByResource.mockResolvedValueOnce(mockImages);

      const result = await imageService.getImagesByResource(
        'event',
        'test-event-id',
        { page: '1', pageSize: '10' }
      );

      expect(result).toEqual({
        images: mockImages,
        total: 2,
      });

      expect(mockRepository.findByResource).toHaveBeenCalledWith(
        'event',
        'test-event-id',
        {
          variant: undefined,
          imageType: undefined,
          limit: 10,
          offset: 0,
        }
      );
    });
  });

  describe('getBatchImages', () => {
    it('should return batch images result', async () => {
      const mockResult = {
        event1: { id: '1', variant: 'medium', file_path: '/path1.jpg' },
        event2: { id: '2', variant: 'medium', file_path: '/path2.jpg' },
        event3: null,
      };

      mockRepository.findBatchByEvents.mockResolvedValue(mockResult);

      const result = await imageService.getBatchImages([
        'event1',
        'event2',
        'event3',
      ]);

      expect(result).toEqual(mockResult);
      expect(mockRepository.findBatchByEvents).toHaveBeenCalledWith(
        ['event1', 'event2', 'event3'],
        undefined,
        undefined
      );
    });

    it('should return batch images with variant filter', async () => {
      const mockResult = {
        event1: { id: '1', variant: 'large', file_path: '/path1.jpg' },
      };

      mockRepository.findBatchByEvents.mockResolvedValue(mockResult);

      const result = await imageService.getBatchImages(['event1'], 'large');

      expect(result).toEqual(mockResult);
      expect(mockRepository.findBatchByEvents).toHaveBeenCalledWith(
        ['event1'],
        'large',
        undefined
      );
    });

    it('should return batch images with imageType filter', async () => {
      const mockResult = {
        event1: { id: '1', image_type: 'poster', file_path: '/path1.jpg' },
      };

      mockRepository.findBatchByEvents.mockResolvedValue(mockResult);

      const result = await imageService.getBatchImages(
        ['event1'],
        undefined,
        'poster'
      );

      expect(result).toEqual(mockResult);
      expect(mockRepository.findBatchByEvents).toHaveBeenCalledWith(
        ['event1'],
        undefined,
        'poster'
      );
    });
  });

  describe('deleteImages', () => {
    it('should delete images successfully', async () => {
      const paths = ['/images/events/test/poster.jpg'];

      (mockR2.delete as any).mockResolvedValue(undefined);
      mockRepository.deleteByPaths.mockResolvedValue(undefined);

      const result = await imageService.deleteImages(paths);

      expect(result).toEqual({
        deletedCount: 1,
        failedPaths: [],
      });

      expect(mockR2.delete).toHaveBeenCalledWith(
        'images/events/test/poster.jpg'
      );
      expect(mockRepository.deleteByPaths).toHaveBeenCalledWith(paths);
    });

    it('should handle R2 deletion failures', async () => {
      const paths = ['/images/events/test/poster.jpg'];

      (mockR2.delete as any).mockRejectedValue(new Error('R2 error'));

      const result = await imageService.deleteImages(paths);

      expect(result).toEqual({
        deletedCount: 0,
        failedPaths: paths,
      });

      expect(mockRepository.deleteByPaths).not.toHaveBeenCalled();
    });
  });
});
