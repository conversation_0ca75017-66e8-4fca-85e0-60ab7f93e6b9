'use client';

import { useState } from 'react';
import { BookmarkButton } from '@/components/bookmark/BookmarkButton';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * 收藏性能测试页面
 * 用于对比优化前后的 API 请求数量
 */
export default function BookmarkPerformanceTestPage() {
  const [showOptimized, setShowOptimized] = useState(true);
  
  // 模拟收藏列表数据
  const mockBookmarks = [
    { id: '1', circleId: 'circle-1', name: 'Tech Community' },
    { id: '2', circleId: 'circle-2', name: 'Design Hub' },
    { id: '3', circleId: 'circle-3', name: 'Developer Group' },
    { id: '4', circleId: 'circle-4', name: 'Startup Network' },
    { id: '5', circleId: 'circle-5', name: 'AI Research' },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Bookmark Performance Test</h1>
        <p className="text-muted-foreground">
          Compare API requests before and after optimization
        </p>
        
        <div className="flex justify-center gap-4">
          <Button 
            variant={showOptimized ? "default" : "outline"}
            onClick={() => setShowOptimized(true)}
          >
            Optimized (with knownStatus)
          </Button>
          <Button 
            variant={!showOptimized ? "default" : "outline"}
            onClick={() => setShowOptimized(false)}
          >
            Unoptimized (without knownStatus)
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {showOptimized ? 'Optimized Version' : 'Unoptimized Version'}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {showOptimized 
              ? 'Each BookmarkButton uses knownStatus prop - no additional API calls'
              : 'Each BookmarkButton queries API individually - 5 extra API calls'
            }
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockBookmarks.map((bookmark) => (
              <div key={bookmark.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">{bookmark.name}</h3>
                  <p className="text-sm text-muted-foreground">Circle ID: {bookmark.circleId}</p>
                </div>
                
                {showOptimized ? (
                  <BookmarkButton
                    circleId={bookmark.circleId}
                    size="sm"
                    variant="ghost"
                    showText={false}
                    knownStatus={{
                      isBookmarked: true,
                      bookmarkId: bookmark.id,
                    }}
                  />
                ) : (
                  <BookmarkButton
                    circleId={bookmark.circleId}
                    size="sm"
                    variant="ghost"
                    showText={false}
                  />
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Performance Impact</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-red-600">Before Optimization</h4>
              <ul className="text-sm space-y-1">
                <li>• 1 API call to get bookmark list</li>
                <li>• 5 additional API calls to check each item's status</li>
                <li>• Total: 6 API calls</li>
                <li>• Redundant data fetching</li>
                <li>• Slower page load</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">After Optimization</h4>
              <ul className="text-sm space-y-1">
                <li>• 1 API call to get bookmark list</li>
                <li>• 0 additional status check calls</li>
                <li>• Total: 1 API call</li>
                <li>• No redundant requests</li>
                <li>• Faster page load</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>How to Monitor</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-2">
            <p>Open your browser's Developer Tools (F12) and go to the Network tab to see the difference:</p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Clear the network log</li>
              <li>Switch between optimized and unoptimized versions</li>
              <li>Count the API requests to <code>/circles/*/bookmark/status</code></li>
              <li>Notice the difference in request count</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
