"use client";

import React, { useState } from 'react';
import { processEventImage, EVENT_IMAGE_VARIANTS } from '@/lib/image-processor';

export default function TestImageProcessorPage() {
  const [results, setResults] = useState<any[]>([]);
  const [processing, setProcessing] = useState(false);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setProcessing(true);
    setResults([]);

    try {
      console.log('Starting image processing...');
      console.log('EVENT_IMAGE_VARIANTS:', EVENT_IMAGE_VARIANTS);
      
      const processedImages = await processEventImage(file, (variant, progress) => {
        console.log(`Processing ${variant}:`, progress);
      });

      console.log('Processing complete. Results:', processedImages);
      setResults(processedImages);
    } catch (error) {
      console.error('Processing failed:', error);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">图片处理器测试</h1>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            选择图片文件
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            disabled={processing}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {processing && (
          <div className="text-blue-600">
            处理中... 请查看控制台日志
          </div>
        )}

        {results.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-4">
              处理结果 ({results.length} 个变体)
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {results.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">{result.name}</h3>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>尺寸: {result.width} × {result.height}</div>
                    <div>大小: {(result.size / 1024).toFixed(1)} KB</div>
                    <div>格式: {result.format}</div>
                  </div>
                  <img
                    src={URL.createObjectURL(result.blob)}
                    alt={result.name}
                    className="mt-2 w-full h-32 object-cover rounded"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="bg-gray-100 p-4 rounded">
          <h3 className="font-medium mb-2">预期变体配置:</h3>
          <pre className="text-sm overflow-x-auto">
            {JSON.stringify(EVENT_IMAGE_VARIANTS, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
