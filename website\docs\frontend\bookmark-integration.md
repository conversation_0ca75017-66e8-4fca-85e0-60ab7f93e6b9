# 收藏模块前端集成指南

> 📝 **版本**: v1.0.0  
> 🕒 **更新时间**: 2025-08-02  
> 👥 **目标读者**: 前端开发者

## 快速开始

### 安装依赖

收藏模块基于项目现有的技术栈，无需额外安装依赖：

```bash
# 项目已包含所需依赖
# - @tanstack/react-query (数据管理)
# - 自动生成的API客户端
# - TypeScript类型定义
```

### 基础使用

```typescript
import { useBookmarks, useToggleBookmark, useBookmarkStatus } from '@/hooks/useBookmarks';

function BookmarkExample() {
  // 获取收藏列表
  const { data: bookmarks, isLoading } = useBookmarks();
  
  // 切换收藏状态
  const toggleMutation = useToggleBookmark();
  
  // 查询特定社团收藏状态
  const { data: status } = useBookmarkStatus('circle-123');

  return (
    <div>
      <button onClick={() => toggleMutation.mutate('circle-123')}>
        {status?.isBookmarked ? '取消收藏' : '收藏'}
      </button>
    </div>
  );
}
```

## 核心 Hooks

### useBookmarks

获取用户收藏列表

```typescript
const { data, isLoading, error } = useBookmarks({
  page: 1,
  pageSize: 20
});

// 返回数据结构
interface BookmarkListResponse {
  items: Array<{
    id: string;
    name: string;
    bookmarkedAt: string;
  }>;
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}
```

### useBookmarkStats

获取收藏统计信息

```typescript
const { data } = useBookmarkStats({ 
  includeIds: true // 包含收藏社团ID列表
});

// 返回数据结构
interface BookmarkStatsResponse {
  totalBookmarks: number;
  bookmarkedCircleIds?: string[];
}
```

### useToggleBookmark

切换收藏状态

```typescript
const toggleMutation = useToggleBookmark();

// 使用方法
toggleMutation.mutate('circle-123', {
  onSuccess: (data) => {
    console.log('收藏状态:', data.isBookmarked);
  },
  onError: (error) => {
    console.error('操作失败:', error);
  }
});
```

### useBookmarkStatus

查询收藏状态

```typescript
const { data, isLoading } = useBookmarkStatus('circle-123');

// 返回数据结构
interface BookmarkStatusResponse {
  isBookmarked: boolean;
}
```

## 性能优化

### 批量查询策略

```typescript
// ✅ 推荐：使用批量查询
function CircleList({ circles }: { circles: Circle[] }) {
  // 一次性获取所有收藏状态
  const { data: stats } = useBookmarkStats({ includeIds: true });
  const bookmarkedIds = new Set(stats?.bookmarkedCircleIds || []);

  return (
    <div>
      {circles.map(circle => (
        <CircleCard 
          key={circle.id}
          circle={circle}
          isBookmarked={bookmarkedIds.has(circle.id)}
        />
      ))}
    </div>
  );
}
```

### 乐观更新

```typescript
function BookmarkButton({ circleId }: { circleId: string }) {
  const queryClient = useQueryClient();
  const { data: status } = useBookmarkStatus(circleId);
  
  const toggleMutation = useToggleBookmark({
    // 乐观更新
    onMutate: async (circleId) => {
      await queryClient.cancelQueries(['bookmark-status', circleId]);
      
      const previousStatus = queryClient.getQueryData(['bookmark-status', circleId]);
      
      queryClient.setQueryData(['bookmark-status', circleId], {
        isBookmarked: !status?.isBookmarked
      });
      
      return { previousStatus };
    },
    
    // 错误回滚
    onError: (err, circleId, context) => {
      queryClient.setQueryData(['bookmark-status', circleId], context?.previousStatus);
    },
    
    // 成功后刷新
    onSettled: () => {
      queryClient.invalidateQueries(['bookmark-status', circleId]);
      queryClient.invalidateQueries(['bookmarks']);
    }
  });

  return (
    <button 
      onClick={() => toggleMutation.mutate(circleId)}
      disabled={toggleMutation.isLoading}
    >
      {status?.isBookmarked ? '❤️' : '🤍'}
    </button>
  );
}
```

## 个性化社团列表

### 基础使用

```typescript
function PersonalizedCircleList() {
  // 自动支持个性化排序（已收藏社团优先）
  const { data: circles } = useCircles({
    page: 1,
    pageSize: 50
  });

  return (
    <div>
      {circles?.items.map(circle => (
        <CircleCard 
          key={circle.id}
          circle={circle}
          // 如果API返回isBookmarked字段，直接使用
          isBookmarked={circle.isBookmarked}
        />
      ))}
    </div>
  );
}
```

### 结合收藏状态

```typescript
function EnhancedCircleList() {
  const { data: circles } = useCircles();
  const { data: stats } = useBookmarkStats({ includeIds: true });
  
  // 合并收藏状态信息
  const circlesWithBookmarkStatus = useMemo(() => {
    if (!circles?.items || !stats?.bookmarkedCircleIds) return circles?.items || [];
    
    const bookmarkedIds = new Set(stats.bookmarkedCircleIds);
    
    return circles.items.map(circle => ({
      ...circle,
      isBookmarked: bookmarkedIds.has(circle.id)
    }));
  }, [circles, stats]);

  return (
    <div>
      {circlesWithBookmarkStatus.map(circle => (
        <CircleCard 
          key={circle.id}
          circle={circle}
          isBookmarked={circle.isBookmarked}
        />
      ))}
    </div>
  );
}
```

## 错误处理

### 统一错误处理

```typescript
function BookmarkErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="p-4 border border-red-200 rounded">
          <h3>收藏功能暂时不可用</h3>
          <p>{error.message}</p>
          <button onClick={resetError}>重试</button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  );
}
```

### Hook级别错误处理

```typescript
function useBookmarkWithErrorHandling(circleId: string) {
  const { data, error, isLoading } = useBookmarkStatus(circleId);
  
  useEffect(() => {
    if (error) {
      // 记录错误
      console.error('收藏状态查询失败:', error);
      
      // 显示用户友好的错误信息
      toast.error('无法获取收藏状态，请稍后重试');
    }
  }, [error]);
  
  return {
    isBookmarked: data?.isBookmarked ?? false,
    isLoading,
    hasError: !!error
  };
}
```

## 最佳实践

### 1. 性能优化
- 使用批量查询减少网络请求
- 实施乐观更新提升用户体验
- 合理使用React Query缓存

### 2. 用户体验
- 提供清晰的加载状态
- 实施错误边界和降级方案
- 保持UI状态一致性

### 3. 代码组织
- 封装可复用的收藏组件
- 使用TypeScript确保类型安全
- 遵循项目代码规范

## 相关文档

- [收藏模块API文档](../api/bookmark-api.md)
- [React Query最佳实践](./react-query-best-practices.md)
- [错误处理指南](./error-handling.md)
