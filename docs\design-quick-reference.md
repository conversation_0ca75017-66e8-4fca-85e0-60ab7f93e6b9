# Ayafeed 设计系统快速参考

## 🎨 核心设计模式

### 页面背景模板
```html
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden">
  <!-- 装饰元素 -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/5 to-blue-600/5 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative">
    <!-- 页面内容 -->
  </div>
</div>
```

### 现代卡片
```html
<div class="overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-2 hover:scale-105 border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl rounded-xl">
  <!-- 内容 -->
</div>
```

### 渐变标题
```html
<h1 class="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
  标题文字
</h1>
```

### 现代按钮
```html
<button class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:bg-white/80 dark:hover:bg-slate-800/80 transition-all duration-300 rounded-xl px-4 py-2">
  按钮
</button>
```

### 现代输入框
```html
<input class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 rounded-xl" />
```

## 🌈 色彩速查

### 渐变背景
- **浅色**: `from-slate-50 via-white to-slate-100`
- **深色**: `dark:from-slate-950 dark:via-slate-900 dark:to-slate-800`

### 装饰色彩
- **蓝紫**: `from-blue-400/10 to-purple-600/10`
- **紫粉**: `from-purple-400/10 to-pink-600/10`
- **青蓝**: `from-cyan-400/5 to-blue-600/5`

### 文字渐变
- **标准**: `from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400`
- **悬停**: `hover:from-blue-600 hover:to-purple-600`

### 边框和背景
- **边框**: `border-slate-200/50 dark:border-slate-700/50`
- **卡片背景**: `from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80`
- **输入背景**: `bg-white/60 dark:bg-slate-800/60`

## ⚡ 动画类名

### 悬停效果
```html
<!-- 提升效果 -->
<div class="hover:-translate-y-2 transition-transform duration-300">

<!-- 缩放效果 -->
<div class="hover:scale-105 transition-transform duration-300">

<!-- 组合效果 -->
<div class="hover:-translate-y-2 hover:scale-105 transition-all duration-300">

<!-- 阴影效果 -->
<div class="hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300">
```

### 图片悬停
```html
<div class="overflow-hidden">
  <img class="group-hover:scale-110 transition-transform duration-300" />
</div>
```

## 📱 响应式网格

### 标准网格
```html
<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
  <!-- 网格项目 -->
</div>
```

### 响应式间距
```html
<div class="px-4 md:px-12 py-6 sm:py-8">
  <!-- 内容 -->
</div>
```

## 🎯 常用组合

### 页面标题区域
```html
<div class="text-center space-y-4 mb-8">
  <h1 class="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
    页面标题
  </h1>
  <p class="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
    页面描述文字
  </p>
</div>
```

### 搜索框区域
```html
<div class="flex justify-center mb-8">
  <input 
    placeholder="搜索..."
    class="max-w-md bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 transition-all duration-300 rounded-xl px-4 py-2"
  />
</div>
```

### 卡片网格
```html
<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
  <div class="overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-2 hover:scale-105 border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl rounded-xl">
    <!-- 卡片内容 -->
  </div>
</div>
```

## 🛠️ React 组件模板

### 页面布局
```tsx
export function ModernPage({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden">
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/5 to-blue-600/5 rounded-full blur-3xl"></div>
      </div>
      <div className="relative">
        {children}
      </div>
    </div>
  )
}
```

### 现代卡片
```tsx
export function ModernCard({ children, hover = true }: { children: React.ReactNode, hover?: boolean }) {
  return (
    <motion.div
      className={`overflow-hidden border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl rounded-xl transition-all duration-300 ${hover ? 'hover:shadow-2xl hover:shadow-blue-500/10' : ''}`}
      whileHover={hover ? { y: -8, scale: 1.02 } : undefined}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  )
}
```

## ✅ 检查清单

### 新页面创建
- [ ] 使用标准背景模板
- [ ] 添加装饰元素
- [ ] 应用渐变标题
- [ ] 使用现代卡片样式
- [ ] 添加悬停动画
- [ ] 测试响应式布局
- [ ] 验证深色模式

### 组件开发
- [ ] 遵循命名规范
- [ ] 使用标准色彩
- [ ] 添加过渡动画
- [ ] 支持深色模式
- [ ] 确保可访问性
- [ ] 优化性能

### 代码审查
- [ ] 类名一致性
- [ ] 动画性能
- [ ] 响应式适配
- [ ] 浏览器兼容性
- [ ] 代码可维护性

---

💡 **提示**: 这个快速参考包含了最常用的设计模式。完整的设计系统文档请参考 `design-system.md`。
