import type { D1Database, KVNamespace } from '@cloudflare/workers-types';

import { createBookmarkRepository } from './repository';
import { createBookmarkCache } from './cache';
import type {
  BookmarkListQuery,
  BookmarkListResponse,
  BookmarkStatusResponse,
  BookmarkStatsResponse,
  BookmarkBatchRequest,
  BookmarkBatchResponse,
} from './schema';

/**
 * 切换收藏状态：若存在则删除（返回 false），否则插入（返回 true）。
 * 返回最新 isBookmarked 状态。
 */
export async function toggleBookmark(
  db: D1Database,
  userId: string,
  circleId: string,
  kv?: KVNamespace
): Promise<{ isBookmarked: boolean }> {
  const repo = createBookmarkRepository(db);
  const isBookmarked = await repo.toggle(userId, circleId);

  // 清除相关缓存
  if (kv) {
    const cache = createBookmarkCache(kv);
    await cache.invalidateBookmarkCache(userId, circleId);
  }

  return { isBookmarked };
}

/**
 * 获取用户收藏列表
 */
export async function getUserBookmarks(
  db: D1Database,
  userId: string,
  query: BookmarkListQuery,
  kv?: KVNamespace
): Promise<BookmarkListResponse> {
  // 尝试从缓存获取
  if (kv) {
    const cache = createBookmarkCache(kv);
    const cached = await cache.getUserBookmarks(userId, query);
    if (cached) {
      return cached;
    }
  }

  // 从数据库获取
  const repo = createBookmarkRepository(db);
  const result = await repo.getUserBookmarks(userId, query);

  // 设置缓存
  if (kv) {
    const cache = createBookmarkCache(kv);
    await cache.setUserBookmarks(userId, query, result);
  }

  return result;
}

/**
 * 检查收藏状态
 */
export async function getBookmarkStatus(
  db: D1Database,
  userId: string,
  circleId: string,
  kv?: KVNamespace
): Promise<BookmarkStatusResponse> {
  // 尝试从缓存获取
  if (kv) {
    const cache = createBookmarkCache(kv);
    const cached = await cache.getBookmarkStatus(userId, circleId);
    if (cached) {
      return cached;
    }
  }

  // 从数据库获取
  const repo = createBookmarkRepository(db);
  const result = await repo.getBookmarkStatus(userId, circleId);

  // 设置缓存
  if (kv) {
    const cache = createBookmarkCache(kv);
    await cache.setBookmarkStatus(userId, circleId, result);
  }

  return result;
}

/**
 * 获取收藏统计
 */
export async function getBookmarkStats(
  db: D1Database,
  userId: string,
  kv?: KVNamespace,
  includeIds = false
): Promise<BookmarkStatsResponse> {
  // 注意：当includeIds=true时，我们不使用缓存，因为ID列表可能很大且变化频繁
  // 只有基础统计数据才使用缓存
  if (!includeIds && kv) {
    const cache = createBookmarkCache(kv);
    const cached = await cache.getBookmarkStats(userId);
    if (cached) {
      return cached;
    }
  }

  // 从数据库获取
  const repo = createBookmarkRepository(db);
  const result = await repo.getBookmarkStats(userId, includeIds);

  // 只缓存基础统计数据（不包含ID列表）
  if (!includeIds && kv) {
    const cache = createBookmarkCache(kv);
    await cache.setBookmarkStats(userId, result);
  }

  return result;
}

/**
 * 批量操作收藏
 */
export async function batchBookmarks(
  db: D1Database,
  userId: string,
  request: BookmarkBatchRequest,
  kv?: KVNamespace
): Promise<BookmarkBatchResponse> {
  const repo = createBookmarkRepository(db);
  const result = await repo.batchBookmarks(userId, request);

  // 清除相关缓存
  if (kv) {
    const cache = createBookmarkCache(kv);
    // 批量操作影响多个收藏，清除用户相关的所有缓存
    await cache.invalidateUserCache(userId);

    // 同时清除每个受影响的社团的缓存
    for (const circleId of [
      ...result.success,
      ...result.failed.map((f) => f.circleId),
    ]) {
      await cache.invalidateBookmarkCache(userId, circleId);
    }
  }

  return result;
}
