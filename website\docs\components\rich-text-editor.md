# 富文本编辑器组件

> 基于 Tiptap 的富文本编辑器实现指南

## 概述

Ayafeed 使用 Tiptap 作为富文本编辑器解决方案，提供了丰富的文本编辑功能和图片上传支持。

## 技术栈

- **Tiptap**: 基于 ProseMirror 的现代富文本编辑器
- **React 19**: 组件框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架

## 安装依赖

```bash
pnpm install @tiptap/react @tiptap/pm @tiptap/starter-kit
pnpm install @tiptap/extension-image @tiptap/extension-link @tiptap/extension-color
pnpm install @tiptap/extension-text-style @tiptap/extension-list-item @tiptap/extension-bullet-list
pnpm install @tiptap/extension-ordered-list @tiptap/extension-text-align
```

## 基础使用

### 1. 创建编辑器组件

```typescript
// components/RichTextEditor.tsx
import React, { useCallback } from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Link from '@tiptap/extension-link'
import Color from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import TextAlign from '@tiptap/extension-text-align'
import BulletList from '@tiptap/extension-bullet-list'
import OrderedList from '@tiptap/extension-ordered-list'
import ListItem from '@tiptap/extension-list-item'

interface RichTextEditorProps {
  content?: string
  onChange?: (content: string) => void
  placeholder?: string
  editable?: boolean
}

export function RichTextEditor({
  content = '',
  onChange,
  placeholder = '开始编写...',
  editable = true
}: RichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Color,
      TextStyle,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'list-disc list-inside',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'list-decimal list-inside',
        },
      }),
      ListItem,
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML())
    },
  })

  const addImage = useCallback(() => {
    const url = window.prompt('请输入图片URL:')
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run()
    }
  }, [editor])

  if (!editor) {
    return null
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden">
      {editable && (
        <div className="border-b border-gray-300 p-2 flex flex-wrap gap-1">
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBold().run()}
            isActive={editor.isActive('bold')}
            title="粗体"
          >
            B
          </ToolbarButton>
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleItalic().run()}
            isActive={editor.isActive('italic')}
            title="斜体"
          >
            I
          </ToolbarButton>
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            isActive={editor.isActive('heading', { level: 2 })}
            title="标题"
          >
            H2
          </ToolbarButton>
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            isActive={editor.isActive('bulletList')}
            title="无序列表"
          >
            •
          </ToolbarButton>
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            isActive={editor.isActive('orderedList')}
            title="有序列表"
          >
            1.
          </ToolbarButton>
          <ToolbarButton
            onClick={addImage}
            title="插入图片"
          >
            📷
          </ToolbarButton>
        </div>
      )}
      <EditorContent
        editor={editor}
        className="prose max-w-none p-4 min-h-[200px] focus:outline-none"
      />
    </div>
  )
}

interface ToolbarButtonProps {
  onClick: () => void
  isActive?: boolean
  title: string
  children: React.ReactNode
}

function ToolbarButton({ onClick, isActive, title, children }: ToolbarButtonProps) {
  return (
    <button
      onClick={onClick}
      title={title}
      className={`px-2 py-1 rounded text-sm font-medium transition-colors ${
        isActive
          ? 'bg-blue-600 text-white'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
    >
      {children}
    </button>
  )
}
```

### 2. 在表单中使用

```typescript
// 在表单组件中使用
import { RichTextEditor } from '@/components/RichTextEditor'
import { useState } from 'react'

export function EventForm() {
  const [description, setDescription] = useState('')

  return (
    <form>
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          活动描述
        </label>
        <RichTextEditor
          content={description}
          onChange={setDescription}
          placeholder="请输入活动描述..."
        />
      </div>
      {/* 其他表单字段 */}
    </form>
  )
}
```

## 图片上传功能

### 1. 图片上传组件

```typescript
// components/ImageUpload.tsx
import { useCallback, useState } from 'react'
import { useEditor } from '@tiptap/react'

interface ImageUploadProps {
  editor: ReturnType<typeof useEditor>
  onUpload?: (file: File) => Promise<string>
}

export function ImageUpload({ editor, onUpload }: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !editor) return

    setUploading(true)
    try {
      let imageUrl: string

      if (onUpload) {
        // 使用自定义上传函数
        imageUrl = await onUpload(file)
      } else {
        // 使用 FileReader 创建本地预览
        imageUrl = await new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result as string)
          reader.readAsDataURL(file)
        })
      }

      editor.chain().focus().setImage({ src: imageUrl }).run()
    } catch (error) {
      console.error('图片上传失败:', error)
    } finally {
      setUploading(false)
    }
  }, [editor, onUpload])

  return (
    <div className="relative">
      <input
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        id="image-upload"
        disabled={uploading}
      />
      <label
        htmlFor="image-upload"
        className={`inline-flex items-center px-3 py-1 rounded text-sm cursor-pointer transition-colors ${
          uploading
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        }`}
      >
        {uploading ? '上传中...' : '📷 上传图片'}
      </label>
    </div>
  )
}
```

### 2. 集成图片上传

```typescript
// 在编辑器中集成图片上传
const uploadImage = async (file: File): Promise<string> => {
  const formData = new FormData()
  formData.append('image', file)

  const response = await fetch('/api/upload/image', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error('上传失败')
  }

  const { url } = await response.json()
  return url
}

// 在工具栏中使用
<ImageUpload editor={editor} onUpload={uploadImage} />
```

## 样式定制

### 1. 编辑器样式

```css
/* globals.css */
.ProseMirror {
  outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
}

.ProseMirror ul, .ProseMirror ol {
  padding-left: 1.5rem;
}

.ProseMirror h1, .ProseMirror h2, .ProseMirror h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.ProseMirror h1 {
  font-size: 1.5rem;
}

.ProseMirror h2 {
  font-size: 1.25rem;
}

.ProseMirror h3 {
  font-size: 1.125rem;
}
```

## 最佳实践

### 1. 性能优化

- 使用 `useMemo` 缓存编辑器配置
- 避免频繁的 `onChange` 调用
- 图片懒加载和压缩

### 2. 用户体验

- 提供清晰的工具栏图标
- 支持键盘快捷键
- 实时保存草稿

### 3. 安全性

- 对用户输入进行 HTML 清理
- 验证图片文件类型和大小
- 防止 XSS 攻击

## 相关文档

- [Tiptap 官方文档](https://tiptap.dev/)
- [图片上传 API](../api/image-upload.md)
- [表单组件指南](./forms.md)
