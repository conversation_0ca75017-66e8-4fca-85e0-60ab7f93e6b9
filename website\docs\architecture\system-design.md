# 系统架构设计

> Ayafeed 前端应用的整体架构设计和技术栈说明。

## 架构概览

```mermaid
graph TD;
  Browser[浏览器] -->|HTTPS| Frontend[Next.js 15 应用];
  Frontend -->|REST API| Backend[后端 API 服务];
  Frontend -->|Static Assets| CDN[静态资源 CDN];
  Backend --> DB[(数据库)];
  Backend --> Cache[(缓存层)];
```

## 技术栈

### 前端应用
- **框架**: Next.js 15 (App Router)
- **UI 库**: React 19
- **类型系统**: TypeScript
- **样式**: Tailwind CSS + Radix UI
- **状态管理**: React Query + Zustand
- **国际化**: next-intl
- **地图**: React-Leaflet

### API 集成
- **客户端生成**: OpenAPI + @openapi-codegen
- **数据获取**: @tanstack/react-query
- **类型安全**: 基于 OpenAPI 规范自动生成
- **错误处理**: 统一的错误处理机制

### 开发工具
- **构建工具**: Next.js 内置 (Turbopack)
- **代码质量**: ESLint + TypeScript
- **测试**: Vitest + Testing Library
- **包管理**: pnpm

## 部署架构

- **前端部署**: Vercel / Netlify / 静态托管
- **API 服务**: 独立部署的后端服务
- **静态资源**: CDN 加速
- **域名**: 支持自定义域名配置

> 架构更新时请同步修改此文档并提交相应的 ADR。