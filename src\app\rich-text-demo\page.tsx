'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { RichTextEditor, ContentManager, MultilingualContentManager } from '@/components/rich-text-editor';
import { useRichTextContentType, type EntityType, type ContentType } from '@/hooks/useRichTextContent';

export default function RichTextDemoPage() {
  const [entityType, setEntityType] = useState<EntityType>('event');
  const [entityId, setEntityId] = useState('demo-event-1');
  const [contentType, setContentType] = useState<ContentType>('introduction');
  const [singleEditorContent, setSingleEditorContent] = useState('');

  const {
    value,
    setValue,
    save,
    isLoading,
    isSaving,
    hasUnsavedChanges,
  } = useRichTextContentType(entityType, entityId, contentType, {
    autoSave: false,
  });

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Rich Text Editor Demo</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          This demo showcases the rich text editor components with content management functionality.
          Try editing content, switching between different entity types, and testing the save functionality.
        </p>
      </div>

      <Tabs defaultValue="multilingual-manager" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="multilingual-manager">Multilingual Manager</TabsTrigger>
          <TabsTrigger value="content-manager">Content Manager</TabsTrigger>
          <TabsTrigger value="single-editor">Single Editor</TabsTrigger>
          <TabsTrigger value="standalone">Standalone Editor</TabsTrigger>
        </TabsList>

        {/* Multilingual Content Manager Demo */}
        <TabsContent value="multilingual-manager" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Multilingual Content Manager Demo</CardTitle>
              <CardDescription>
                Full multilingual content management interface with language switching
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="entity-type-ml">Entity Type</Label>
                  <Select value={entityType} onValueChange={(value) => setEntityType(value as EntityType)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="event">Event</SelectItem>
                      <SelectItem value="venue">Venue</SelectItem>
                      <SelectItem value="circle">Circle</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="entity-id-ml">Entity ID</Label>
                  <Input
                    id="entity-id-ml"
                    value={entityId}
                    onChange={(e) => setEntityId(e.target.value)}
                    placeholder="Enter entity ID"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <MultilingualContentManager
            entityType={entityType}
            entityId={entityId}
            autoSave={false}
          />
        </TabsContent>

        {/* Content Manager Demo */}
        <TabsContent value="content-manager" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Content Manager Demo</CardTitle>
              <CardDescription>
                Full content management interface with multiple content types
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="entity-type">Entity Type</Label>
                  <Select value={entityType} onValueChange={(value) => setEntityType(value as EntityType)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="event">Event</SelectItem>
                      <SelectItem value="venue">Venue</SelectItem>
                      <SelectItem value="circle">Circle</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="entity-id">Entity ID</Label>
                  <Input
                    id="entity-id"
                    value={entityId}
                    onChange={(e) => setEntityId(e.target.value)}
                    placeholder="Enter entity ID"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <ContentManager
            entityType={entityType}
            entityId={entityId}
            autoSave={false}
          />
        </TabsContent>

        {/* Single Editor Demo */}
        <TabsContent value="single-editor" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Single Content Type Editor</CardTitle>
              <CardDescription>
                Editor for a specific content type with integrated content management
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="content-type">Content Type</Label>
                  <Select value={contentType} onValueChange={(value) => setContentType(value as ContentType)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="introduction">Introduction</SelectItem>
                      <SelectItem value="highlights">Highlights</SelectItem>
                      <SelectItem value="guide">Guide</SelectItem>
                      <SelectItem value="notices">Notices</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="entity-type-2">Entity Type</Label>
                  <Select value={entityType} onValueChange={(value) => setEntityType(value as EntityType)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="event">Event</SelectItem>
                      <SelectItem value="venue">Venue</SelectItem>
                      <SelectItem value="circle">Circle</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="entity-id-2">Entity ID</Label>
                  <Input
                    id="entity-id-2"
                    value={entityId}
                    onChange={(e) => setEntityId(e.target.value)}
                    placeholder="Enter entity ID"
                  />
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={save}
                  disabled={!hasUnsavedChanges || isSaving}
                  variant="default"
                >
                  {isSaving ? 'Saving...' : 'Save Content'}
                </Button>
                {hasUnsavedChanges && (
                  <span className="text-sm text-amber-600">Unsaved changes</span>
                )}
              </div>

              {isLoading ? (
                <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
              ) : (
                <RichTextEditor
                  content={value}
                  onChange={setValue}
                  onSave={save}
                  placeholder={`Enter ${contentType} content...`}
                  autoSave={false}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Standalone Editor Demo */}
        <TabsContent value="standalone" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Standalone Rich Text Editor</CardTitle>
              <CardDescription>
                Basic rich text editor without content management integration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RichTextEditor
                content={singleEditorContent}
                onChange={setSingleEditorContent}
                placeholder="Start writing your content here..."
                autoSave={false}
              />
              
              <div className="mt-4 p-4 bg-gray-50 rounded">
                <h4 className="font-semibold mb-2">HTML Output:</h4>
                <pre className="text-sm bg-white p-2 rounded border overflow-x-auto">
                  {singleEditorContent || '<p></p>'}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Features</CardTitle>
          <CardDescription>Rich text editor capabilities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Text Formatting</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Bold, Italic, Underline</li>
                <li>• Strikethrough, Code</li>
                <li>• Text Colors</li>
                <li>• Text Alignment</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Structure</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Headings (H1, H2, H3)</li>
                <li>• Bullet Lists</li>
                <li>• Ordered Lists</li>
                <li>• Blockquotes</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Media & Links</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Links</li>
                <li>• Images</li>
                <li>• Undo/Redo</li>
                <li>• Auto-save</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
