/**
 * 富文本编辑器图片上传 Hook
 */

import { toast } from 'sonner';
import { usePostContentApiUploadImages } from '@/api/generated/ayafeedComponents';

interface UseRichTextImageUploadOptions {
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
  showToast?: boolean;
}

/**
 * 富文本编辑器图片上传 Hook
 */
export function useRichTextImageUpload(options: UseRichTextImageUploadOptions = {}) {
  const { onSuccess, onError, showToast = true } = options;

  return usePostContentApiUploadImages({
    onSuccess: (data) => {
      if (showToast) {
        toast.success('图片上传成功');
      }
      onSuccess?.(data.url);
    },
    onError: (error: Error) => {
      console.error('Rich text image upload failed:', error);

      if (showToast) {
        toast.error(error.message || '图片上传失败');
      }

      onError?.(error);
    },
  });
}

/**
 * 创建图片上传处理函数
 * 用于 Tiptap 编辑器的图片上传
 */
export function createImageUploadHandler(
  uploadMutation: ReturnType<typeof useRichTextImageUpload>,
  editor?: any
) {
  return () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      uploadMutation.mutate({
        body: {
          image: file,
        },
      }, {
        onSuccess: (data) => {
          // 插入图片到编辑器
          editor?.chain().focus().setImage({ src: data.url }).run();
        },
      });
    };
    input.click();
  };
}
