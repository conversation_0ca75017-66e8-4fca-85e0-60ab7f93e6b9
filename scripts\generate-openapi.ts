import { execSync } from 'child_process';
import { writeFileSync, mkdirSync, readFileSync } from 'fs';
import path from 'path';

import { OpenAPIHono } from '@hono/zod-openapi';
import minimist from 'minimist';

// 导入各路由
import { routes as admin } from '@/modules/admin';
import { routes as appearanceRoutes } from '@/modules/appearance';
import { routes as artistRoutes } from '@/modules/artist';
// import { routes as auth } from '@/modules/auth'; // 已替换为 Better Auth
import { betterAuthOpenAPI } from '@/modules/better-auth/openapi';
import { circleBookmarkRoutes, userBookmarkRoutes } from '@/modules/bookmark';
import { routes as circleRoutes } from '@/modules/circle';
import { routes as richTextRoutes } from '@/modules/rich-text';
import { routes as eventRoutes } from '@/modules/event';
import { routes as feedRoutes } from '@/modules/feed';
import { routes as imageRoutes } from '@/modules/images';
import { routes as searchRoutes } from '@/modules/search';
import { pubVenues } from '@/modules/venue/routes';

// 解析命令行参数
const argv = minimist(process.argv.slice(2), {
  boolean: ['bump'],
  default: { bump: false },
});

// 读取 package.json 以同步版本号
const pkgPath = path.resolve(__dirname, '../package.json');
const pkgJson = JSON.parse(readFileSync(pkgPath, 'utf-8'));

// 仅在 CI 环境或显式 --bump 参数时递增版本号
const shouldBumpVersion = process.env.CI === 'true' || argv.bump;

let apiVersion = pkgJson.version;
if (shouldBumpVersion) {
  const semverMatch = /^([0-9]+)\.([0-9]+)\.([0-9]+)(?:\.([0-9]+))?$/.exec(
    pkgJson.version ?? '0.0.0.0'
  );
  if (!semverMatch) {
    throw new Error(
      `Invalid semver in package.json.version: ${pkgJson.version}`
    );
  }
  const [, /* all */ major, minor, milestone, patch = '0'] = semverMatch;
  apiVersion = `${major}.${minor}.${milestone}.${Number(patch) + 1}`;

  // 更新 package.json version 字段并回写磁盘
  pkgJson.version = apiVersion;
  writeFileSync(pkgPath, JSON.stringify(pkgJson, null, 2));
  console.log(`📦 Bumped version to ${apiVersion}`);
}

// 全局 tags：统一使用点号分隔格式（Admin.*）
const globalTags = [
  { name: 'Auth', description: '认证相关接口' },
  { name: 'Admin.Circles', description: '后台社团管理接口' },
  { name: 'Admin.Events', description: '后台展会管理接口' },
  { name: 'Admin.Users', description: '后台用户管理接口' },
  { name: 'Admin.Images', description: '后台图片管理接口' },
  { name: 'Admin.Venues', description: '后台场馆管理接口' },
  { name: 'Admin.Logs', description: '后台日志接口' },
  { name: 'Admin.Stats', description: '后台统计接口' },
  { name: 'Events', description: '展会公开接口' },
  { name: 'Venues', description: '场馆公开接口' },
  { name: 'Circles', description: '社团公开接口' },
  { name: 'Artists', description: '作者公开接口' },
  { name: 'Appearances', description: '参展记录公开接口' },
  { name: 'Bookmarks', description: '收藏功能接口' },
  { name: 'Search', description: '搜索功能接口' },
  { name: 'Feed', description: 'Feed 流接口' },
  { name: 'Images', description: '图片公开接口' },
  { name: 'Rich Text', description: '富文本内容管理接口' },
];

// 新增：全局服务器列表（Production / Local）
const globalServers = [
  { url: 'https://api.example.com', description: 'Production server' },
  { url: 'http://localhost:8787', description: 'Local dev server' },
];

// 更新全局安全方案：Bearer + Cookie Auth
const globalSecuritySchemes = {
  bearerAuth: {
    type: 'http',
    scheme: 'bearer',
    bearerFormat: 'JWT',
  },
  cookieAuth: {
    type: 'apiKey',
    in: 'cookie',
    name: 'refresh_token',
  },
};

// 组装临时 Hono 实例以聚合 OpenAPI
const app = new OpenAPIHono();

app.route('/auth', betterAuthOpenAPI);
app.route('/events', eventRoutes);
app.route('/venues', pubVenues);
app.route('/circles', circleRoutes);
app.route('/circles', circleBookmarkRoutes);
app.route('/', userBookmarkRoutes);
app.route('/artists', artistRoutes);
app.route('/appearances', appearanceRoutes);
app.route('/search', searchRoutes);
app.route('/feed', feedRoutes);
app.route('/images', imageRoutes);
app.route('/rich-text', richTextRoutes);
app.route('/admin', admin);

const spec = app.getOpenAPIDocument({
  openapi: '3.0.0',
  info: { title: 'Ayafeed API', version: apiVersion },
});

// 手动合并全局 tags、servers 与安全方案，避免类型定义限制
// --- 合并全局 tags ---
const existingTags = (spec as any).tags ?? [];
// 以 name 作为唯一键，后出现的 tag 会覆盖先前的同名 tag（保证使用 globalTags 中的描述）
const tagMap: Record<string, any> = {};
for (const tag of [...existingTags, ...globalTags]) {
  tagMap[tag.name] = tag;
}
(spec as any).tags = Object.values(tagMap);

(spec as any).servers = globalServers;
(spec as any).components = {
  ...((spec as any).components ?? {}),
  securitySchemes: {
    ...((spec as any).components?.securitySchemes ?? {}),
    ...globalSecuritySchemes,
  },
};

// ---------- 为所有 Operation 自动生成唯一 operationId ----------
{
  const existingIds = new Set<string>();
  const paths = (spec as any).paths as Record<string, Record<string, any>>;
  for (const [pathKey, pathItem] of Object.entries(paths)) {
    for (const [method, operation] of Object.entries(pathItem)) {
      if (typeof operation !== 'object') continue;
      // 若已显式提供则跳过
      if (operation.operationId && !existingIds.has(operation.operationId)) {
        existingIds.add(operation.operationId);
        continue;
      }
      // 根据 method + path 生成基础 ID
      const sanitizedPath = pathKey
        .replace(/\{([^}]+)\}/g, '$1') // 去除花括号
        .replace(/\//g, '_') // 斜杠替换为下划线
        .replace(/[^A-Za-z0-9_]/g, '') // 去除非法字符
        .replace(/^_+|_+$/g, '') // 去除首尾下划线
        .replace(/_+/g, '_'); // 合并多余下划线
      let baseId = `${method.toLowerCase()}_${sanitizedPath}`;
      // 若仍为空（如根路径 “/”），特殊处理
      if (!sanitizedPath) baseId = method.toLowerCase();

      let uniqueId = baseId;
      let suffix = 1;
      while (existingIds.has(uniqueId)) {
        uniqueId = `${baseId}_${suffix++}`;
      }
      operation.operationId = uniqueId;
      existingIds.add(uniqueId);
    }
  }
}
// ---------- 自动生成 operationId 结束 ----------

// 输出位置：统一写入 docs-site/static/openapi.json，供 openapi-typescript 与 CI 检查使用
const outDir = path.resolve(__dirname, '../docs-site/static');

mkdirSync(outDir, { recursive: true });
const outPath = path.join(outDir, 'openapi.json');
writeFileSync(outPath, JSON.stringify(spec, null, 2));

// 同时写入项目根目录，方便 CI & 其他工具直接引用
const rootOutPath = path.resolve(__dirname, '../openapi.json');
writeFileSync(rootOutPath, JSON.stringify(spec, null, 2));

// 生成 TypeScript 类型定义
const typesOutPath = path.resolve(__dirname, '@/api-types.d.ts');
try {
  execSync(`npx openapi-typescript ${rootOutPath} --output ${typesOutPath}`);
  console.log(`✅ TypeScript types generated at ${typesOutPath}`);
} catch (error) {
  console.error('❌ Failed to generate TypeScript types:', error);
  process.exit(1);
}

console.log(`✅ OpenAPI spec generated at ${outPath}`);
console.log(`✅ OpenAPI spec also written to ${rootOutPath}`);
