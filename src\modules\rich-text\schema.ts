import { z } from '@hono/zod-openapi';

// 实体类型枚举
export const EntityTypeEnum = z.enum(['event', 'venue', 'circle']);
export type EntityType = z.infer<typeof EntityTypeEnum>;

// 内容类型枚举
export const ContentTypeEnum = z.enum([
  'introduction',
  'highlights',
  'guide',
  'notices',
]);
export type ContentType = z.infer<typeof ContentTypeEnum>;

// 富文本内容实体 Schema
export const richTextContentSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  entity_type: EntityTypeEnum.openapi({ example: 'event' }),
  entity_id: z.string().openapi({ example: 'reitaisai-22' }),
  content_type: ContentTypeEnum.openapi({ example: 'introduction' }),
  content: z.string().openapi({ example: '<p>这是一个富文本内容示例</p>' }),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

export type RichTextContent = z.infer<typeof richTextContentSchema>;

// 创建内容请求 Schema
export const createContentRequest = z.object({
  entity_type: EntityTypeEnum,
  entity_id: z.string().min(1, '实体ID不能为空'),
  content_type: ContentTypeEnum,
  content: z.string().max(100000, '内容长度不能超过100000字符'),
});

export type CreateContentRequest = z.infer<typeof createContentRequest>;

// 更新内容请求 Schema
export const updateContentRequest = z.object({
  content: z.string().max(100000, '内容长度不能超过100000字符'),
});

export type UpdateContentRequest = z.infer<typeof updateContentRequest>;

// 批量内容更新 Schema
export const batchContentRequest = z.object({
  introduction: z.string().max(100000).optional(),
  highlights: z.string().max(100000).optional(),
  guide: z.string().max(100000).optional(),
  notices: z.string().max(100000).optional(),
});

export type BatchContentRequest = z.infer<typeof batchContentRequest>;

// 内容查询响应 Schema
export const contentResponse = z.object({
  introduction: z.string().optional(),
  highlights: z.string().optional(),
  guide: z.string().optional(),
  notices: z.string().optional(),
});

export type ContentResponse = z.infer<typeof contentResponse>;

// 内容列表响应 Schema
export const contentListResponse = z.array(richTextContentSchema);
export type ContentListResponse = z.infer<typeof contentListResponse>;
