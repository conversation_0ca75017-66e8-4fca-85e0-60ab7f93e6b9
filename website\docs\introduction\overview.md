---
id: overview
title: 项目概述
sidebar_label: 概述
sidebar_position: 1
description: Ayafeed 同人展会信息聚合平台的项目概述和技术架构
keywords: [ayafeed, 同人展会, Next.js, React, TypeScript]
---

# Ayafeed 项目概述

## 1. 项目简介

Ayafeed 是一个现代化的同人展会信息聚合平台，致力于为同人社团、展会组织者和爱好者提供高质量的信息管理和展示服务。

### 核心特性

- 🌐 **多语言支持** - 支持中文、日文、英文三种语言
- 🔐 **完整的认证系统** - 用户登录、注册、权限管理
- 📱 **响应式设计** - 适配桌面端和移动端
- 🚀 **现代化技术栈** - Next.js 15 + React 19 + TypeScript
- 🎨 **优雅的UI** - 基于Tailwind CSS的现代化界面
- ⚡ **高性能** - 使用React Query进行数据管理和缓存
- 🛡️ **类型安全** - 基于OpenAPI自动生成的类型定义

## 2. 技术架构

### 前端技术栈
- **框架**: Next.js 15 (App Router)
- **UI库**: React 19
- **类型系统**: TypeScript
- **样式**: Tailwind CSS + Radix UI
- **状态管理**: React Query + Zustand
- **国际化**: next-intl
- **地图**: React-Leaflet
- **富文本**: Tiptap

### 开发工具
- **构建**: Next.js 内置 (Turbopack)
- **代码质量**: ESLint + TypeScript
- **测试**: Vitest + Testing Library
- **包管理**: pnpm

## 3. 核心功能

### 展会管理
- 展会信息的创建、编辑和展示
- 多语言展会名称和描述
- 场馆信息和地理位置
- 展会时间和排序

### 社团管理
- 社团基本信息管理
- 社交平台链接
- 参展历史记录
- 作品和作者关联

### 用户系统
- 用户注册和登录
- 角色权限管理
- 个人资料管理
- 收藏和订阅功能

### 内容管理
- 富文本编辑器
- 图片上传和管理
- 搜索和筛选
- 内容流展示

## 4. 项目结构

```
src/
├── app/                    # Next.js App Router 页面
├── components/            # React 组件
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具库
├── services/             # 业务服务层
├── stores/               # 状态管理
├── types/                # TypeScript 类型定义
└── utils/                # 工具函数
```

## 5. 开发规范

### 代码质量
- 使用 TypeScript 严格模式
- 遵循 ESLint 规则
- 保持 80% 以上的测试覆盖率
- 使用 Prettier 格式化代码

### 组件设计
- 优先使用函数组件和 Hooks
- 组件职责单一，易于测试
- 支持可访问性和国际化
- 使用 Radix UI 确保组件质量

### 性能优化
- 使用 React Query 进行数据缓存
- 实现代码分割和懒加载
- 优化图片和静态资源
- 监控 Web Vitals 指标

## 6. 快速开始

### 环境要求
- Node.js 18+
- pnpm 8+

### 安装和运行
```bash
# 克隆项目
git clone https://github.com/your-username/ayafeed.git
cd ayafeed

# 安装依赖
pnpm install

# 配置环境变量
cp env.example .env.local

# 生成API客户端代码
pnpm gen:api

# 启动开发服务器
pnpm dev
```

### 开发命令
```bash
# 开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 代码检查
pnpm lint

# 运行测试
pnpm test

# 生成API客户端
pnpm gen:api
```

## 7. 相关文档

- [快速开始](../getting-started/installation.md) - 详细的安装和配置指南
- [前端架构](../architecture/frontend.md) - 技术架构详细说明
- [API集成](../frontend/client-generation.md) - API客户端使用指南
- [组件文档](../components/rich-text-editor.md) - 组件使用说明
- [贡献指南](../guides/contribution.md) - 如何参与项目开发