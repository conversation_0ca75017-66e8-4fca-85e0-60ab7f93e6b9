import { drizzle } from 'drizzle-orm/d1';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { betterAuth } from 'better-auth';
import { betterAuthOptions } from './options';

/**
 * Better Auth Instance for Cloudflare D1
 */
export const auth = (env: CloudflareBindings) => {
  // 使用 Cloudflare D1 数据库
  const db = drizzle(env.DB);

  return betterAuth({
    ...betterAuthOptions,
    database: drizzleAdapter(db, { provider: 'sqlite' }), // D1 基于 SQLite
    baseURL: env.BETTER_AUTH_URL,
    secret: env.BETTER_AUTH_SECRET,
  });
};
