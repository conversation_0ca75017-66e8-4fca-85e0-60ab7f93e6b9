import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import { errorResponse } from '@/utils/schemas';
import { HonoApp } from '@/types';

/**
 * Better Auth OpenAPI 定义
 * 手动定义 Better Auth 端点的 OpenAPI 文档
 */
export const betterAuthOpenAPI = new OpenAPIHono<HonoApp>();

// ---------- Schema 定义 ----------

const userSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  email: z.string().email().openapi({ example: '<EMAIL>' }),
  name: z.string().openapi({ example: 'Alice' }),
  username: z.string().optional().openapi({ example: 'alice_user' }),
  role: z
    .enum(['admin', 'editor', 'viewer', 'user'])
    .openapi({ example: 'user' }),
  emailVerified: z.boolean().openapi({ example: false }),
  createdAt: z
    .string()
    .datetime()
    .openapi({ example: '2024-01-01T00:00:00.000Z' }),
  updatedAt: z
    .string()
    .datetime()
    .openapi({ example: '2024-01-01T00:00:00.000Z' }),
});

const sessionSchema = z.object({
  id: z.string().openapi({ example: 'session-123' }),
  userId: z.string().openapi({ example: 'uuid-123' }),
  expiresAt: z
    .string()
    .datetime()
    .openapi({ example: '2024-02-01T00:00:00.000Z' }),
  ipAddress: z.string().optional().openapi({ example: '***********' }),
  userAgent: z.string().optional().openapi({ example: 'Mozilla/5.0...' }),
});

const authResponseSchema = z.object({
  user: userSchema,
  session: sessionSchema,
});

// ---------- 注册端点 ----------
const signUpRoute = createRoute({
  method: 'post',
  path: '/sign-up/email',
  summary: 'Register a new user with email',
  tags: ['Better Auth'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            email: z.string().email().openapi({
              example: '<EMAIL>',
              description: 'User email address',
            }),
            password: z.string().min(8).openapi({
              example: 'password123',
              description: 'User password (minimum 8 characters)',
            }),
            name: z.string().optional().openapi({
              example: 'Alice',
              description: 'User display name',
            }),
            username: z.string().optional().openapi({
              example: 'alice_user',
              description: 'Unique username (3-30 characters)',
            }),
          }),
        },
      },
    },
  },
  responses: {
    201: {
      description: 'User registered successfully',
      content: {
        'application/json': {
          schema: authResponseSchema,
        },
      },
    },
    400: {
      description: 'Bad Request',
      content: {
        'application/json': {
          schema: errorResponse,
        },
      },
    },
    409: {
      description: 'Email or username already exists',
      content: {
        'application/json': {
          schema: z.object({
            error: z.string().openapi({ example: 'Email already exists' }),
          }),
        },
      },
    },
  },
});

// ---------- 邮箱登录端点 ----------
const signInEmailRoute = createRoute({
  method: 'post',
  path: '/sign-in/email',
  summary: 'Sign in with email and password',
  tags: ['Better Auth'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            email: z.string().email().openapi({
              example: '<EMAIL>',
              description: 'User email address',
            }),
            password: z.string().openapi({
              example: 'password123',
              description: 'User password',
            }),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      description: 'Login successful',
      content: {
        'application/json': {
          schema: authResponseSchema,
        },
      },
    },
    400: {
      description: 'Bad Request',
      content: {
        'application/json': {
          schema: errorResponse,
        },
      },
    },
    401: {
      description: 'Invalid credentials',
      content: {
        'application/json': {
          schema: z.object({
            error: z.string().openapi({ example: 'Invalid email or password' }),
          }),
        },
      },
    },
  },
});

// ---------- 用户名登录端点 ----------
const signInUsernameRoute = createRoute({
  method: 'post',
  path: '/sign-in/username',
  summary: 'Sign in with username and password',
  tags: ['Better Auth'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: z.object({
            username: z.string().openapi({
              example: 'alice_user',
              description: 'Username',
            }),
            password: z.string().openapi({
              example: 'password123',
              description: 'User password',
            }),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      description: 'Login successful',
      content: {
        'application/json': {
          schema: authResponseSchema,
        },
      },
    },
    400: {
      description: 'Bad Request',
      content: {
        'application/json': {
          schema: errorResponse,
        },
      },
    },
    401: {
      description: 'Invalid credentials',
      content: {
        'application/json': {
          schema: z.object({
            error: z
              .string()
              .openapi({ example: 'Invalid username or password' }),
          }),
        },
      },
    },
  },
});

// ---------- 登出端点 ----------
const signOutRoute = createRoute({
  method: 'post',
  path: '/sign-out',
  summary: 'Sign out current user',
  tags: ['Better Auth'],
  responses: {
    200: {
      description: 'Logout successful',
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean().openapi({ example: true }),
          }),
        },
      },
    },
    400: {
      description: 'Bad Request',
      content: {
        'application/json': {
          schema: errorResponse,
        },
      },
    },
  },
});

// ---------- 获取会话端点 ----------
const getSessionRoute = createRoute({
  method: 'get',
  path: '/session',
  summary: 'Get current user session',
  tags: ['Better Auth'],
  responses: {
    200: {
      description: 'Session retrieved successfully',
      content: {
        'application/json': {
          schema: authResponseSchema,
        },
      },
    },
    401: {
      description: 'Not authenticated',
      content: {
        'application/json': {
          schema: z.object({
            error: z.string().openapi({ example: 'Not authenticated' }),
          }),
        },
      },
    },
  },
});

// ---------- 注册路由（仅用于 OpenAPI 文档生成） ----------
// 这些处理器不会被实际调用，仅用于 OpenAPI 文档生成
// Better Auth 的实际处理在 src/app.ts 中通过 app.on(['POST', 'GET'], '/auth/*', ...) 完成

betterAuthOpenAPI.openapi(signUpRoute, (c: any) => {
  // 虚拟处理器，仅用于 OpenAPI 文档生成
  throw new Error('This endpoint is handled by Better Auth');
});

betterAuthOpenAPI.openapi(signInEmailRoute, (c: any) => {
  throw new Error('This endpoint is handled by Better Auth');
});

betterAuthOpenAPI.openapi(signInUsernameRoute, (c: any) => {
  throw new Error('This endpoint is handled by Better Auth');
});

betterAuthOpenAPI.openapi(signOutRoute, (c: any) => {
  throw new Error('This endpoint is handled by Better Auth');
});

betterAuthOpenAPI.openapi(getSessionRoute, (c: any) => {
  throw new Error('This endpoint is handled by Better Auth');
});
