import type { D1Database } from '@cloudflare/workers-types';
import { v4 as uuidv4 } from 'uuid';

import type { Image } from './schema';

export interface ImageRepository {
  create(data: CreateImageData): Promise<Image>;
  findById(id: string): Promise<Image | null>;
  findByGroupId(groupId: string): Promise<Image[]>;
  findByResource(
    resourceType: string,
    resourceId: string,
    options?: FindOptions
  ): Promise<Image[]>;
  findBatchByEvents(
    eventIds: string[],
    variant?: string,
    imageType?: string
  ): Promise<Record<string, Image | null>>;
  deleteByPaths(paths: string[]): Promise<string[]>; // 返回成功删除的路径
  deleteByGroupId(groupId: string): Promise<number>; // 返回删除的数量
}

export interface CreateImageData {
  groupId: string;
  resourceType: string;
  resourceId: string;
  imageType: string;
  variant: string;
  filePath: string;
  fileSize?: number;
  width?: number;
  height?: number;
  format?: string;
}

export interface FindOptions {
  variant?: string;
  imageType?: string;
  limit?: number;
  offset?: number;
}

export class D1ImageRepository implements ImageRepository {
  constructor(private readonly db: D1Database) {}

  async create(data: CreateImageData): Promise<Image> {
    const id = uuidv4();
    const now = new Date().toISOString();

    const stmt = this.db.prepare(`
      INSERT INTO images (
        id, group_id, resource_type, resource_id, image_type, variant,
        file_path, file_size, width, height, format, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    await stmt
      .bind(
        id,
        data.groupId,
        data.resourceType,
        data.resourceId,
        data.imageType,
        data.variant,
        data.filePath,
        data.fileSize || null,
        data.width || null,
        data.height || null,
        data.format || null,
        now,
        now
      )
      .run();

    return this.findById(id) as Promise<Image>;
  }

  async findById(id: string): Promise<Image | null> {
    const stmt = this.db.prepare('SELECT * FROM images WHERE id = ?');
    const result = await stmt.bind(id).first();
    return (result as Image) || null;
  }

  async findByGroupId(groupId: string): Promise<Image[]> {
    const stmt = this.db.prepare(`
      SELECT * FROM images 
      WHERE group_id = ? 
      ORDER BY variant, created_at DESC
    `);
    const { results } = await stmt.bind(groupId).all();
    return results as Image[];
  }

  async findByResource(
    resourceType: string,
    resourceId: string,
    options: FindOptions = {}
  ): Promise<Image[]> {
    let sql = `
      SELECT * FROM images
      WHERE resource_type = ? AND resource_id = ?
    `;
    const params: any[] = [resourceType, resourceId];

    if (options.variant) {
      sql += ' AND variant = ?';
      params.push(options.variant);
    }

    if (options.imageType) {
      sql += ' AND image_type = ?';
      params.push(options.imageType);
    }

    sql += ' ORDER BY image_type, variant, created_at DESC';

    if (options.limit) {
      sql += ' LIMIT ?';
      params.push(options.limit);

      if (options.offset) {
        sql += ' OFFSET ?';
        params.push(options.offset);
      }
    }

    const stmt = this.db.prepare(sql);
    const { results } = await stmt.bind(...params).all();

    return results as Image[];
  }

  async findBatchByEvents(
    eventIds: string[],
    variant?: string,
    imageType?: string
  ): Promise<Record<string, Image | null>> {
    if (eventIds.length === 0) {
      return {};
    }

    const placeholders = eventIds.map(() => '?').join(',');
    let sql = `
      SELECT * FROM images
      WHERE resource_type = 'event'
      AND resource_id IN (${placeholders})
    `;

    const params: any[] = [...eventIds];

    if (variant) {
      sql += ' AND variant = ?';
      params.push(variant);
    }

    if (imageType) {
      sql += ' AND image_type = ?';
      params.push(imageType);
    }

    sql += ' ORDER BY resource_id, image_type, variant';

    const stmt = this.db.prepare(sql);
    const { results } = await stmt.bind(...params).all();
    const images = results as Image[];

    // 组织返回结果，确保每个 eventId 都有对应的条目
    const result: Record<string, Image | null> = {};
    eventIds.forEach((eventId) => {
      const image = images.find((img) => img.resource_id === eventId);
      result[eventId] = image || null;
    });

    return result;
  }

  async deleteByPaths(paths: string[]): Promise<string[]> {
    if (paths.length === 0) return [];

    const placeholders = paths.map(() => '?').join(',');
    const stmt = this.db.prepare(
      `DELETE FROM images WHERE file_path IN (${placeholders})`
    );

    try {
      await stmt.bind(...paths).run();
      return paths; // 假设全部成功，实际应该检查受影响的行数
    } catch (error) {
      console.error('Failed to delete images by paths:', error);
      return []; // 实际应该返回成功删除的路径
    }
  }

  async deleteByGroupId(groupId: string): Promise<number> {
    const stmt = this.db.prepare('DELETE FROM images WHERE group_id = ?');
    const result = await stmt.bind(groupId).run();
    return result.meta?.changes || (result as any).changes || 0;
  }
}

export function createImageRepository(db: D1Database): ImageRepository {
  return new D1ImageRepository(db);
}
