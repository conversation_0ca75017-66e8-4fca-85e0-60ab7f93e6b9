/**
 * 日期排序工具函数
 * 用于处理 date_sort 字段的转换
 */

/**
 * 将 date_sort 数字转换为日期字符串 (YYYY-MM-DD)
 * @param dateSort - 8位数字格式的日期 (YYYYMMDD)
 * @returns 日期字符串或空字符串
 */
export function dateSortToDateString(dateSort: number | undefined): string {
  if (!dateSort) return ""
  const dateStr = dateSort.toString()
  if (dateStr.length !== 8) return ""

  const year = dateStr.substring(0, 4)
  const month = dateStr.substring(4, 6)
  const day = dateStr.substring(6, 8)
  return `${year}-${month}-${day}`
}

/**
 * 将日期字符串转换为 date_sort 数字
 * @param dateString - 日期字符串 (YYYY-MM-DD)
 * @returns 8位数字格式的日期或 undefined
 */
export function dateStringToDateSort(dateString: string): number | undefined {
  if (!dateString) return undefined
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return undefined

  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")
  return parseInt(`${year}${month}${day}`)
}

/**
 * 验证 date_sort 格式是否正确
 * @param dateSort - 要验证的数字
 * @returns 是否为有效的 date_sort 格式
 */
export function isValidDateSort(dateSort: number): boolean {
  const dateStr = dateSort.toString()
  if (dateStr.length !== 8) return false

  const year = parseInt(dateStr.substring(0, 4))
  const month = parseInt(dateStr.substring(4, 6))
  const day = parseInt(dateStr.substring(6, 8))

  // 基本范围检查
  if (year < 1900 || year > 2100) return false
  if (month < 1 || month > 12) return false
  if (day < 1 || day > 31) return false

  // 使用 Date 对象验证日期是否真实存在
  const date = new Date(year, month - 1, day)
  return (
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day
  )
}

/**
 * 从日期字符串生成 date_sort (兼容旧版本)
 * @param dateStr - 各种格式的日期字符串
 * @returns date_sort 数字或 undefined
 */
export function generateDateSort(dateStr: string): number | undefined {
  // 尝试匹配常见的日期格式
  const match = dateStr.match(/(\d{4})[年\-/](\d{1,2})[月\-/](\d{1,2})/)
  if (match) {
    const [, year, month, day] = match
    return parseInt(`${year}${month.padStart(2, "0")}${day.padStart(2, "0")}`)
  }

  // 尝试解析为 Date 对象
  const date = new Date(dateStr)
  if (!isNaN(date.getTime())) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, "0")
    const day = date.getDate().toString().padStart(2, "0")
    return parseInt(`${year}${month}${day}`)
  }

  return undefined
}
