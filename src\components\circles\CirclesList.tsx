import { VirtuosoGrid } from "react-virtuoso";
import Link from "next/link";
import Image from "next/image";
import { useMemo, useRef, useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookmarkCompactButton } from "@/components/bookmark";
import { useBookmarkManager } from "@/hooks/useBookmark";
import CirclesEmptyState from "./CirclesEmptyState";

interface Circle {
  id: string;
  name: string;
  urls?: string | null;
  created_at: string;
  updated_at: string;
}

interface CirclesListProps {
  circles: Circle[];
  isLoading?: boolean;
  isLoadingMore?: boolean;
  hasMore?: boolean;
  searchKeyword?: string;
  onLoadMore?: () => void;
  onResetFilters?: () => void;
  total?: number;
}

export default function CirclesList({
  circles,
  isLoading,
  isLoadingMore,
  hasMore,
  searchKeyword,
  onLoadMore,
  onResetFilters,
  total
}: CirclesListProps) {
  // 使用优化的收藏状态管理器
  const { isBookmarked, bookmarkedIds } = useBookmarkManager();

  // 使用 ref 保存稳定的排序列表，避免收藏状态变化时重新排序
  const stableSortedCircles = useRef<Circle[]>([]);
  const mountTime = useRef(Date.now());
  const [, forceRender] = useState({});

  // 使用 ref 保存最新的状态，避免闭包陷阱
  const latestCircles = useRef(circles);
  const latestBookmarkedIds = useRef(bookmarkedIds);

  // 更新 ref 中的最新值
  latestCircles.current = circles;
  latestBookmarkedIds.current = bookmarkedIds;

  // 排序函数 - 使用传入的参数而不是闭包中的值
  const sortCircles = (circleList: Circle[], bookmarkIds: Set<string>) => {
    return [...circleList].sort((a, b) => {
      const aBookmarked = bookmarkIds.has(a.id);
      const bBookmarked = bookmarkIds.has(b.id);

      // 已收藏的排在前面
      if (aBookmarked && !bBookmarked) return -1;
      if (!aBookmarked && bBookmarked) return 1;

      // 其他情况保持原有顺序
      return 0;
    });
  };

  // 智能排序：只在初始化期间允许排序，避免用户操作时重排序
  useEffect(() => {
    const timeSinceMount = Date.now() - mountTime.current;

    // 只在组件挂载后的前3秒内允许排序（初始化期间）
    // 这样可以处理数据异步加载的情况，但避免用户操作时的重排序
    if (timeSinceMount < 3000 && circles.length > 0) {
      stableSortedCircles.current = sortCircles(circles, bookmarkedIds);
      forceRender({}); // 强制重新渲染
    }

    // 数据长度变化时也要排序（加载更多数据）
    else if (timeSinceMount >= 3000 && circles.length !== stableSortedCircles.current.length) {
      stableSortedCircles.current = sortCircles(circles, bookmarkedIds);
      forceRender({}); // 强制重新渲染
    }
  }, [circles, bookmarkedIds]); // 依赖两者，但用时间限制避免用户操作时重排序



  // 监听页面可见性变化，标签页切换回来时重新排序
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && latestCircles.current.length > 0) {
        // 使用最新的状态进行排序
        stableSortedCircles.current = sortCircles(latestCircles.current, latestBookmarkedIds.current);
        forceRender({}); // 强制重新渲染
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []); // 空依赖数组，避免重复绑定事件

  // 使用稳定的排序列表
  const sortedCircles = stableSortedCircles.current.length > 0
    ? stableSortedCircles.current
    : circles;

  if (isLoading) {
    return <CirclesListSkeleton />;
  }

  if (circles.length === 0 && !isLoading) {
    return (
      <CirclesEmptyState
        type="no-results"
        searchKeyword={searchKeyword}
        onReset={onResetFilters}
      />
    );
  }

  return (
    <div className="w-full">
      <VirtuosoGrid
        data={sortedCircles}
        style={{ height: "calc(100vh - 200px)" }}
        listClassName="w-full grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4"
        itemClassName="w-full"
        overscan={200}
        endReached={onLoadMore}
        itemContent={(index, circle) => (
          <CircleCard
            key={circle.id}
            circle={circle}
            isBookmarked={isBookmarked(circle.id)}
          />
        )}
        components={{
          Footer: () => (
            <InfiniteScrollFooter
              isLoadingMore={isLoadingMore}
              hasMore={hasMore}
              total={circles.length}
              totalAvailable={total}
            />
          )
        }}
      />
    </div>
  );
}

// 社团卡片组件
function CircleCard({ circle, isBookmarked }: { circle: Circle; isBookmarked: boolean }) {
  // 解析 URLs JSON
  let logoUrl = "/images/circles/placeholder.svg";
  let authorName = "";
  let twitterUrl = "";
  let webUrl = "";

  try {
    const urls = JSON.parse(circle.urls || "{}");
    logoUrl = urls.logo_url || logoUrl;
    authorName = urls.author || "";
    twitterUrl = urls.twitter_url || "";
    webUrl = urls.web_url || "";
  } catch {
    // 使用默认值
  }



  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 group border relative ${
      isBookmarked
        ? 'bg-blue-100 dark:bg-blue-900/40 border-blue-300 dark:border-blue-700 hover:border-blue-400 dark:hover:border-blue-600 shadow-blue-100/50 dark:shadow-blue-900/20'
        : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
    }`}>
      {/* 收藏按钮 - 绝对定位在右上角 */}
      <div className="absolute top-2 right-2 z-10">
        <BookmarkCompactButton
          circleId={circle.id}
          className={`transition-opacity duration-200 ${
            isBookmarked ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
          }`}
          knownStatus={{
            isBookmarked,
            bookmarkId: isBookmarked ? 'known' : null
          }}
        />
      </div>

      <Link
        href={`/circles/${circle.id}`}
        className="h-full block focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-xl"
        aria-label={`查看 ${circle.name} 的详细信息${authorName ? `，作者：${authorName}` : ""}`}
      >
        <CardHeader className="pb-3 px-4 pt-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <Image
                src={logoUrl}
                alt={`${circle.name} 的 logo`}
                width={40}
                height={40}
                className="w-10 h-10 rounded-lg object-cover ring-1 ring-border group-hover:ring-primary/40 transition-all duration-200"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = "/images/circles/placeholder.svg";
                }}
              />
            </div>
            <div className="flex-1 min-w-0 space-y-1 pr-8">
              <CardTitle className="text-sm font-semibold leading-tight group-hover:text-primary transition-colors duration-200 line-clamp-2">
                {circle.name}
              </CardTitle>
              {authorName && (
                <p className="text-xs text-muted-foreground line-clamp-1">
                  {authorName}
                </p>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-4 pb-4 pt-0">
          <div className="flex flex-wrap gap-1.5" role="list" aria-label="社团标签">
            {twitterUrl && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-0.5 rounded-md border-border/60 hover:border-primary/40 transition-colors font-medium"
                role="listitem"
              >
                Twitter
              </Badge>
            )}
            {webUrl && (
              <Badge
                variant="outline"
                className="text-xs px-2 py-0.5 rounded-md border-border/60 hover:border-primary/40 transition-colors font-medium"
                role="listitem"
              >
                Web
              </Badge>
            )}
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}

// 无限滚动底部组件
function InfiniteScrollFooter({
  isLoadingMore,
  hasMore,
  total,
  totalAvailable
}: {
  isLoadingMore?: boolean;
  hasMore?: boolean;
  total: number;
  totalAvailable?: number;
}) {
  if (isLoadingMore) {
    return (
      <div className="col-span-full flex justify-center py-8">
        <div className="flex items-center gap-3 text-muted-foreground">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
          <span className="text-sm">加载更多社团中...</span>
        </div>
      </div>
    );
  }

  if (!hasMore && total > 0) {
    return (
      <div className="col-span-full text-center py-8">
        <div className="text-muted-foreground space-y-1">
          <p className="text-sm">已显示全部社团</p>
          <p className="text-xs">
            共 {total} 个社团
            {totalAvailable && totalAvailable !== total && ` (筛选自 ${totalAvailable} 个)`}
          </p>
        </div>
      </div>
    );
  }

  return null;
}

// 骨架屏组件
function CirclesListSkeleton() {
  return (
    <div className="grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4">
      {Array.from({ length: 20 }).map((_, index) => (
        <Card key={index} className="h-full border border-border/50 bg-card">
          <CardHeader className="pb-3 px-4 pt-4">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 rounded-lg bg-muted animate-pulse flex-shrink-0" />
              <div className="flex-1 space-y-1">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-4 pb-4 pt-0">
            <div className="flex gap-1.5">
              <div className="h-5 bg-muted rounded-md w-12 animate-pulse" />
              <div className="h-5 bg-muted rounded-md w-10 animate-pulse" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
