/**
 * 富文本内容 API 客户端
 * 连接到后端 content API
 */

import { getCurrentLocale } from '@/lib/locale-utils';

export type EntityType = 'event' | 'venue' | 'circle';
export type ContentType = 'introduction' | 'highlights' | 'guide' | 'notices';

export interface ContentData {
  introduction?: string;
  highlights?: string;
  guide?: string;
  notices?: string;
}

export interface ContentResponse {
  message: string;
  data: ContentData;
}

export interface SingleContentResponse {
  message: string;
  data: {
    content: string;
  };
}

// 新的 API 响应格式（直接返回数据，不包装）
export interface DirectContentResponse extends ContentData {}

export interface DirectSingleContentResponse {
  content: string;
}

class ContentApiClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    locale?: string
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const targetLocale = locale || getCurrentLocale();

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-Locale': targetLocale,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  /**
   * 获取实体的所有内容
   */
  async getContent(entityType: EntityType, entityId: string, locale?: string): Promise<ContentData> {
    try {
      const response = await this.request<DirectContentResponse>(
        `/rich-text/${entityType}/${entityId}/content`,
        {},
        locale
      );
      return response || {};
    } catch (error) {
      console.error('Failed to get content:', error);
      return {};
    }
  }

  /**
   * 获取实体的特定类型内容
   */
  async getContentByType(
    entityType: EntityType,
    entityId: string,
    contentType: ContentType,
    locale?: string
  ): Promise<string> {
    try {
      const response = await this.request<DirectSingleContentResponse>(
        `/rich-text/${entityType}/${entityId}/content/${contentType}`,
        {},
        locale
      );
      return response.content || '';
    } catch (error) {
      console.error('Failed to get content by type:', error);
      return '';
    }
  }

  /**
   * 创建或更新单个内容
   */
  async createOrUpdateContent(
    entityType: EntityType,
    entityId: string,
    contentType: ContentType,
    content: string,
    locale?: string
  ): Promise<void> {
    await this.request<any>(
      `/rich-text/${entityType}/${entityId}/content`,
      {
        method: 'POST',
        body: JSON.stringify({
          entity_type: entityType,
          entity_id: entityId,
          content_type: contentType,
          content,
        }),
      },
      locale
    );
  }

  /**
   * 批量更新内容
   */
  async batchUpdateContent(
    entityType: EntityType,
    entityId: string,
    content: ContentData,
    locale?: string
  ): Promise<ContentData> {
    const response = await this.request<DirectContentResponse>(
      `/rich-text/${entityType}/${entityId}/content`,
      {
        method: 'PUT',
        body: JSON.stringify(content),
      },
      locale
    );
    return response || {};
  }

  /**
   * 删除实体的所有内容
   */
  async deleteEntityContent(entityType: EntityType, entityId: string): Promise<void> {
    await this.request<any>(
      `/rich-text/${entityType}/${entityId}/content`,
      {
        method: 'DELETE',
      }
    );
  }

  /**
   * 富文本编辑器图片上传
   */
  async uploadImage(file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch(`${this.baseUrl}/rich-text/api/upload/images`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Image upload failed: ${response.status} - ${errorText}`);
    }

    return response.json();
  }
}

// 导出单例实例
export const contentApiClient = new ContentApiClient();

// 兼容性导出，保持与现有代码的兼容
export const apiClient = {
  async getContent(entityType: EntityType, entityId: string, locale?: string): Promise<ContentData> {
    return contentApiClient.getContent(entityType, entityId, locale);
  },

  async updateContent(
    entityType: EntityType,
    entityId: string,
    content: ContentData,
    locale?: string
  ): Promise<ContentData> {
    return contentApiClient.batchUpdateContent(entityType, entityId, content, locale);
  },

  async updateSingleContent(
    entityType: EntityType,
    entityId: string,
    contentType: ContentType,
    content: string,
    locale?: string
  ): Promise<void> {
    return contentApiClient.createOrUpdateContent(entityType, entityId, contentType, content, locale);
  },
};
