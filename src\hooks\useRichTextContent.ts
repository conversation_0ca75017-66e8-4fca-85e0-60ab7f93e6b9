'use client';

import { useState, useCallback, useEffect, useMemo } from 'react';
import { toast } from 'sonner';
import { richTextTranslations } from '@/utils/translation';
import {
  useGetRichtextEntityTypeEntityIdContent,
  usePutRichtextEntityTypeEntityIdContent,
  usePostRichtextEntityTypeEntityIdContent,
  type GetRichtextEntityTypeEntityIdContentResponse,
} from '@/api/generated/ayafeedComponents';


export type EntityType = 'event' | 'venue' | 'circle';
export type ContentType = 'introduction' | 'highlights' | 'guide' | 'notices';
export type Locale = 'zh' | 'ja' | 'en';

// 多语言内容数据结构
export interface MultilingualContentData {
  introduction?: Record<Locale, string>;
  highlights?: Record<Locale, string>;
  guide?: Record<Locale, string>;
  notices?: Record<Locale, string>;
}

// 单语言内容数据结构（向后兼容）
export type ContentData = GetRichtextEntityTypeEntityIdContentResponse;

export interface UseRichTextContentOptions {
  entityType: EntityType;
  entityId: string;
  locale?: Locale;
  autoSave?: boolean;
  autoSaveDelay?: number;
}



export function useRichTextContent({
  entityType,
  entityId,
  locale = 'zh',
  autoSave = false,
  autoSaveDelay = 2000,
}: UseRichTextContentOptions) {
  const [localContent, setLocalContent] = useState<ContentData>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 根据语言生成实际的 entityId（添加语言后缀）
  const localizedEntityId = `${entityId}-${locale}`;

  // 获取内容
  const {
    data: rawContent,
    isLoading,
    error,
    refetch,
  } = useGetRichtextEntityTypeEntityIdContent({
    pathParams: {
      entityType,
      entityId: localizedEntityId,
    },
  }, {
    staleTime: 5 * 60 * 1000, // 5分钟
  });

  // 处理API响应格式 - 提取实际的内容数据
  const content = useMemo(() => {
    if (!rawContent) return {};

    // 如果响应包含 data 字段，提取其中的内容
    if (typeof rawContent === 'object' && 'data' in rawContent && rawContent.data) {
      return rawContent.data as ContentData;
    }

    // 否则直接使用响应数据
    return rawContent as ContentData;
  }, [rawContent]);

  // 批量更新内容
  const updateMutation = usePutRichtextEntityTypeEntityIdContent({
    onSuccess: (updatedContent: ContentData) => {
      setLocalContent(updatedContent);
      setHasUnsavedChanges(false);
      toast.success(richTextTranslations.saveSuccess());
      refetch(); // 重新获取数据以确保同步
    },
    onError: (error) => {
      console.error('Failed to save content:', error);
      toast.error(richTextTranslations.saveFailed());
    },
  });

  // 单个内容更新
  const updateSingleMutation = usePostRichtextEntityTypeEntityIdContent({
    onSuccess: (_, variables) => {
      // 直接更新本地状态，避免重新获取数据
      setLocalContent(prev => ({
        ...prev,
        [variables.body.content_type]: variables.body.content
      }));
      setHasUnsavedChanges(false);
      toast.success(richTextTranslations.saveSuccess());
      refetch(); // 重新获取数据以确保同步
    },
    onError: (error) => {
      console.error('Failed to save content:', error);
      toast.error(richTextTranslations.saveFailed());
    },
  });

  // 初始化本地内容
  useEffect(() => {
    if (content && Object.keys(content).length > 0) {
      setLocalContent(content);
    }
  }, [content]);

  // 更新单个内容类型
  const updateContentType = useCallback((contentType: ContentType, value: string) => {
    setLocalContent(prev => ({
      ...prev,
      [contentType]: value,
    }));
    setHasUnsavedChanges(true);
  }, []);

  // 保存所有内容
  const saveAll = useCallback(() => {
    updateMutation.mutate({
      pathParams: {
        entityType,
        entityId: localizedEntityId,
      },
      body: localContent,
    });
  }, [localContent, updateMutation, entityType, localizedEntityId]);

  // 保存单个内容
  const saveSingle = useCallback((contentType: ContentType) => {
    const contentValue = localContent[contentType];
    if (contentValue !== undefined) {
      updateSingleMutation.mutate({
        pathParams: {
          entityType,
          entityId: localizedEntityId,
        },
        body: {
          entity_type: entityType,
          entity_id: localizedEntityId,
          content_type: contentType,
          content: contentValue,
        },
      });
    }
  }, [localContent, updateSingleMutation, entityType, localizedEntityId]);

  // 重置到服务器状态
  const reset = useCallback(() => {
    setLocalContent(content);
    setHasUnsavedChanges(false);
  }, [content]);

  // 自动保存
  useEffect(() => {
    if (!autoSave || !hasUnsavedChanges) return;

    const timer = setTimeout(() => {
      saveAll();
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [autoSave, hasUnsavedChanges, autoSaveDelay, saveAll]);

  return {
    // 数据
    content: localContent,
    serverContent: content,
    isLoading,
    error,
    hasUnsavedChanges,
    
    // 操作
    updateContentType,
    saveAll,
    saveSingle,
    reset,
    refetch,
    
    // 状态
    isSaving: updateMutation.isPending || updateSingleMutation.isPending,
    saveError: updateMutation.error || updateSingleMutation.error,
  };
}

// 单个内容类型的Hook
export function useRichTextContentType(
  entityType: EntityType,
  entityId: string,
  contentType: ContentType,
  options?: {
    locale?: Locale;
    autoSave?: boolean;
    autoSaveDelay?: number;
  }
) {
  const {
    content,
    updateContentType,
    saveSingle,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    error,
  } = useRichTextContent({
    entityType,
    entityId,
    locale: options?.locale,
    autoSave: options?.autoSave,
    autoSaveDelay: options?.autoSaveDelay,
  });

  const value = content[contentType] || '';

  const setValue = useCallback((newValue: string) => {
    updateContentType(contentType, newValue);
  }, [updateContentType, contentType]);

  const save = useCallback(() => {
    saveSingle(contentType);
  }, [saveSingle, contentType]);

  return {
    value,
    setValue,
    save,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    error,
  };
}
