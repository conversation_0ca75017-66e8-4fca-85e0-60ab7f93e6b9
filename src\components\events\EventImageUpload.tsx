"use client";

import React, { useState, useCallback, useRef } from 'react';
import { Upload, X, Image as ImageIcon, Loader2, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

import { useImageProcessor, useFileValidation, useImagePreview } from '@/hooks/useImageProcessor';
import { useEventImage } from '@/hooks/useEventImage';
import { EventImageCard } from './EventImageCard';
import { ImagePreview } from '@/components/images/ImagePreview';
import { useQueryClient } from '@tanstack/react-query';
import { uploadImage } from '@/services/imageService';
import type { ImageInfo } from '@/types/image';

// 事件图片上传组件的 Props
export interface EventImageUploadProps {
  /** 事件 ID */
  eventId: string;
  /** 当前图片组 ID（可选，用于替换现有图片） */
  groupId?: string;
  /** 上传成功回调 */
  onUploadSuccess?: (images: any[]) => void;
  /** 上传失败回调 */
  onUploadError?: (error: Error) => void;
  /** 上传进度回调 */
  onUploadProgress?: (variant: string, progress: number) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

export function EventImageUpload({
  eventId,
  groupId,
  onUploadSuccess,
  onUploadError,
  onUploadProgress,
  disabled = false,
  className
}: EventImageUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewState, setPreviewState] = useState({
    isOpen: false,
    currentIndex: 0,
    images: [] as ImageInfo[],
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  // Hooks
  const { validateFile } = useFileValidation();
  const { previews, createPreview, clearPreviews } = useImagePreview();
  const { 
    processImage, 
    isProcessing, 
    progress: processingProgress, 
    error: processingError,
    reset: resetProcessor 
  } = useImageProcessor({
    onProgress: (variant, progress) => {
      onUploadProgress?.(variant, progress.progress);
    }
  });

  // 查询已上传的图片（不指定变体，获取所有图片）
  const { data: existingImagesData, isLoading: isLoadingImages, error: imagesError } = useEventImage(eventId);

  // 确保 existingImages 是数组
  const existingImages = Array.isArray(existingImagesData) ? existingImagesData :
                        existingImagesData ? [existingImagesData] : [];



  // 使用状态管理顺序上传
  const [isBatchUploading, setIsBatchUploading] = useState(false);
  const [batchUploadProgress, setBatchUploadProgress] = useState(0);
  const [batchUploadError, setBatchUploadError] = useState<string | null>(null);

  // 文件选择处理
  const handleFileSelect = useCallback((file: File) => {
    const validation = validateFile(file);
    if (!validation.valid) {
      onUploadError?.(new Error(validation.error));
      return;
    }

    setSelectedFile(file);
    createPreview(file, 'selected');
  }, [validateFile, createPreview, onUploadError]);

  // 文件输入变化
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  // 拖拽处理
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  // 点击上传区域
  const handleUploadAreaClick = useCallback(() => {
    if (!disabled && !isProcessing && !isBatchUploading) {
      fileInputRef.current?.click();
    }
  }, [disabled, isProcessing, isBatchUploading]);

  // 移除文件
  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    clearPreviews();
    resetProcessor();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [clearPreviews, resetProcessor]);

  // 重置状态
  const handleReset = useCallback(() => {
    setSelectedFile(null);
    clearPreviews();
    resetProcessor();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [clearPreviews, resetProcessor]);

  // 处理图片预览
  const handlePreview = useCallback((image: ImageInfo, index: number) => {
    setPreviewState({
      isOpen: true,
      currentIndex: index,
      images: existingImages,
    });
  }, [existingImages]);

  // 关闭预览
  const closePreview = useCallback(() => {
    setPreviewState(prev => ({ ...prev, isOpen: false }));
  }, []);

  // 预览导航
  const handlePreviewNext = useCallback(() => {
    setPreviewState(prev => ({
      ...prev,
      currentIndex: Math.min(prev.currentIndex + 1, existingImages.length - 1),
    }));
  }, [existingImages.length]);

  const handlePreviewPrevious = useCallback(() => {
    setPreviewState(prev => ({
      ...prev,
      currentIndex: Math.max(prev.currentIndex - 1, 0),
    }));
  }, []);



  // 顺序上传函数
  const sequentialUpload = useCallback(async (uploadRequests: any[]) => {
    setIsBatchUploading(true);
    setBatchUploadError(null);
    setBatchUploadProgress(0);

    const results = [];
    let sharedGroupId: string | undefined;

    try {
      for (let i = 0; i < uploadRequests.length; i++) {
        const request = uploadRequests[i];

        // 第一个变体不传递 groupId（触发清理），后续变体使用返回的 groupId
        const uploadData = {
          ...request,
          groupId: i === 0 ? undefined : sharedGroupId
        };

        console.log(`Uploading variant ${i + 1}/${uploadRequests.length}:`, {
          variant: uploadData.variant,
          groupId: uploadData.groupId || '(will trigger cleanup and auto-generate)'
        });

        // 使用 uploadImage 服务直接上传
        const result = await uploadImage(uploadData);
        results.push(result);

        // 保存第一个变体返回的 groupId
        if (i === 0 && result.groupId) {
          sharedGroupId = result.groupId;
          console.log('Using groupId for subsequent uploads:', sharedGroupId);
        }

        // 更新进度
        setBatchUploadProgress(Math.round(((i + 1) / uploadRequests.length) * 100));
      }

      // 上传成功
      queryClient.invalidateQueries({
        queryKey: ['event-images', eventId]
      });

      onUploadSuccess?.(results);
      handleReset();

      console.log(`Successfully uploaded ${results.length} variants`);
    } catch (error) {
      console.error('Sequential upload failed:', error);
      setBatchUploadError(error instanceof Error ? error.message : '上传失败');
      onUploadError?.(error as Error);
    } finally {
      setIsBatchUploading(false);
    }
  }, [eventId, queryClient, onUploadSuccess, onUploadError, handleReset]);

  // 开始上传
  const handleUpload = useCallback(async () => {
    if (!selectedFile) return;

    try {
      // 处理图片生成变体
      const processedImages = await processImage(selectedFile);

      console.log('Processed images:', processedImages.map(img => ({
        name: img.name,
        size: img.blob.size,
        type: img.blob.type
      })));

      // 准备上传请求
      const uploadRequests = processedImages.map(({ blob, name }) => {
        // 获取原始文件的扩展名
        const originalName = selectedFile.name;
        const lastDotIndex = originalName.lastIndexOf('.');
        const baseName = lastDotIndex > 0 ? originalName.substring(0, lastDotIndex) : originalName;
        const extension = lastDotIndex > 0 ? originalName.substring(lastDotIndex) : '.jpg';

        // 构造新的文件名，保持正确的扩展名
        const newFileName = `${baseName}_${name}${extension}`;

        return {
          file: new File([blob], newFileName, { type: blob.type }),
          category: 'event' as const,
          resourceId: eventId,
          imageType: 'poster' as const,
          variant: name as any
        };
      });

      // 执行顺序上传
      await sequentialUpload(uploadRequests);
    } catch (error) {
      console.error('Upload process failed:', error);
    }
  }, [selectedFile, processImage, eventId, sequentialUpload]);

  // 计算总体进度
  const getTotalProgress = useCallback(() => {
    const variants = Object.keys(processingProgress);
    if (variants.length === 0) return 0;

    const totalProgress = variants.reduce((sum, variant) => {
      return sum + processingProgress[variant].progress;
    }, 0);

    return Math.round(totalProgress / variants.length);
  }, [processingProgress]);

  const isUploading = isProcessing || isBatchUploading;
  const hasError = processingError || batchUploadError;
  const previewUrl = previews['selected'];

  return (
    <div className={cn('space-y-4', className)}>
      {/* 已上传图片预览 */}
      {existingImages && existingImages.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-muted-foreground">
            当前图片 ({existingImages.length} 个变体)
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {existingImages
              .filter(image => image && image.id && image.relativePath) // 过滤无效数据
              .map((image, index) => (
                <EventImageCard
                  key={image.id}
                  image={image}
                  onPreview={(image) => {
                    handlePreview(image, index);
                  }}
                  showActions={true}
                />
              ))}
          </div>
        </div>
      )}

      {/* 上传区域 */}
      <Card className={cn(
        'border-2 border-dashed transition-colors cursor-pointer',
        isDragOver && 'border-primary bg-primary/5',
        isUploading && 'pointer-events-none opacity-50',
        hasError && 'border-destructive'
      )}>
        <CardContent className="p-6">
          <div
            className="flex flex-col items-center justify-center space-y-4 text-center"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleUploadAreaClick}
          >
            {selectedFile ? (
              // 已选择文件的预览
              <div className="space-y-4 w-full">
                <div className="relative inline-block">
                  {previewUrl ? (
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-32 h-24 object-cover rounded-lg border"
                    />
                  ) : (
                    <div className="w-32 h-24 bg-muted rounded-lg flex items-center justify-center">
                      <ImageIcon className="w-8 h-8 text-muted-foreground" />
                    </div>
                  )}
                  
                  {!isUploading && (
                    <Button
                      size="sm"
                      variant="destructive"
                      className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveFile();
                      }}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  )}
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium">{selectedFile.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            ) : (
              // 空状态
              <>
                <Upload className="w-12 h-12 text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">选择图片文件</p>
                  <p className="text-sm text-muted-foreground">
                    拖拽图片到此处，或点击选择文件
                  </p>
                  <p className="text-xs text-muted-foreground">
                    支持 JPEG、PNG、WebP 格式，最大 10MB
                  </p>
                </div>
              </>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled || isUploading}
          />
        </CardContent>
      </Card>

      {/* 进度显示 */}
      {isUploading && (
        <Card>
          <CardContent className="p-4 space-y-3">
            <div className="flex items-center space-x-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm font-medium">
                {isProcessing ? '正在处理图片...' : '正在上传...'}
              </span>
            </div>
            
            <Progress value={getTotalProgress()} className="w-full" />
            
            {/* 详细进度 */}
            {Object.entries(processingProgress).map(([variant, progress]) => (
              <div key={variant} className="text-xs text-muted-foreground">
                {variant}: {progress.stage} ({progress.progress}%)
              </div>
            ))}
          </CardContent>
        </Card>
      )}



      {/* 操作按钮 */}
      {selectedFile && !isUploading && (
        <div className="flex space-x-2">
          <Button
            onClick={handleUpload}
            disabled={disabled || isUploading}
            className="flex-1"
          >
            {isUploading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                上传中...
              </>
            ) : (
              '开始上传'
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isUploading}
          >
            重新选择
          </Button>
        </div>
      )}

      {/* 图片预览 */}
      <ImagePreview
        isOpen={previewState.isOpen}
        images={previewState.images}
        currentIndex={previewState.currentIndex}
        onClose={closePreview}
        onNext={handlePreviewNext}
        onPrevious={handlePreviewPrevious}
        showActions={false} // 不显示删除等操作，因为这是事件图片管理
      />
    </div>
  );
}
