/**
 * Bookmark 模块组件导出
 * 
 * 提供完整的收藏功能组件集合：
 * - BookmarkButton: 收藏按钮组件
 * - BookmarkList: 收藏列表组件  
 * - BookmarkStats: 收藏统计组件
 */

export { 
  BookmarkButton, 
  BookmarkIconButton, 
  BookmarkCompactButton 
} from './BookmarkButton';

export { BookmarkList } from './BookmarkList';

export { BookmarkStats } from './BookmarkStats';

export {
  BookmarkButtonSkeleton,
  BookmarkCompactButtonSkeleton,
  BookmarkStatsSkeleton,
  BookmarkListSkeleton,
  BookmarkGridSkeleton,
  BookmarkEmptySkeleton,
} from './BookmarkSkeleton';

// 重新导出相关 hooks 以便统一导入
export {
  useToggleBookmark,
  useBookmarkStatus,
  useBookmarks,
  useBookmarkStats,
  validateCircleId,
} from '@/hooks/useBookmark';

// 重新导出类型定义
export type {
  PostCirclesCircleIdBookmarkResponse,
  GetCirclesCircleIdBookmarkStatusResponse,
  GetUserBookmarksResponse,
  GetUserBookmarksStatsResponse,
} from '@/hooks/useBookmark';
