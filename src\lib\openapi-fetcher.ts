import { AyafeedContext } from "@/api/generated/ayafeedContext";
import { request } from "@/lib/http";

/**
 * 自定义 fetcher，供 @openapi-codegen 生成的 React Query hooks 调用。
 * 内部统一串联 Toast、401/403 跳转等逻辑，确保行为一致。
 */
export type ErrorWrapper<TError> =
  | TError
  | { status: "unknown"; payload: string };

export type OpenapiFetcherOptions<
  TBody,
  THeaders,
  TQueryParams,
  TPathParams,
> = {
  url: string;
  method: string;
  body?: TBody;
  headers?: THeaders;
  queryParams?: TQueryParams;
  pathParams?: TPathParams;
  signal?: AbortSignal;
} & AyafeedContext["fetcherOptions"];

type Primitive = string | number | boolean | null | undefined;

 
export async function ayafeedFetch<
  TData = unknown,
  TError = unknown, // 保留仅为向后兼容，内部不会使用
  TBody = unknown,
  THeaders = unknown,
  TQueryParams extends Record<string, Primitive> = Record<string, Primitive>,
  TPathParams extends Record<string, Primitive> = Record<string, Primitive>
>(
  options: OpenapiFetcherOptions<TBody, THeaders, TQueryParams, TPathParams>
): Promise<TData> {
  const toStringRecord = (obj: Record<string, Primitive> | undefined) =>
    Object.fromEntries(
      Object.entries(obj ?? {}).map(([k, v]) => [k, String(v ?? "")])
    );

  const resolvedUrl = resolveUrl(
    options.url,
    toStringRecord(options.queryParams),
    toStringRecord(options.pathParams)
  );

  // 直接使用request函数，它已经包含了认证和错误处理
  const result = await request<TData>(resolvedUrl, {
    method: options.method.toUpperCase(),
    body: options.body
      ? options.body instanceof FormData
        ? options.body
        : JSON.stringify(options.body)
      : undefined,
    headers: options.headers,
    signal: options.signal,
  });

  return result;
}

const resolveUrl = (
  url: string,
  queryParams: Record<string, string> = {},
  pathParams: Record<string, string> = {},
) => {
  let query = new URLSearchParams(queryParams).toString();
  if (query) query = `?${query}`;
  return (
    url.replace(/\{\w*\}/g, (key) => pathParams[key.slice(1, -1)] ?? "") +
    query
  );
};

// 兼容旧名称（若有其他地方直接引用 openapiFetcher 可继续使用）
export const openapiFetcher = ayafeedFetch;


