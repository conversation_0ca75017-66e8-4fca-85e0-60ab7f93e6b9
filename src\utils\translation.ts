/**
 * 翻译工具函数
 * 用于在非组件环境中获取翻译文本
 */

import { i18nService } from '@/services/i18n';

// 翻译映射
const translations = {
  zh: {
    richTextEditor: {
      errors: {
        saveFailed: '内容保存失败',
        saveSuccess: '内容保存成功'
      }
    }
  },
  ja: {
    richTextEditor: {
      errors: {
        saveFailed: 'コンテンツの保存に失敗しました',
        saveSuccess: 'コンテンツが正常に保存されました'
      }
    }
  },
  en: {
    richTextEditor: {
      errors: {
        saveFailed: 'Failed to save content',
        saveSuccess: 'Content saved successfully'
      }
    }
  }
} as const;

/**
 * 获取翻译文本
 * @param key 翻译键，支持点分隔的嵌套键
 * @param params 参数对象，用于替换模板变量
 * @returns 翻译后的文本
 */
export function getTranslation(key: string, params?: Record<string, string | number>): string {
  const locale = i18nService.getCurrentLocale();
  const keys = key.split('.');
  
  let value: any = translations[locale];
  
  for (const k of keys) {
    value = value?.[k];
  }
  
  if (typeof value !== 'string') {
    console.warn(`Translation missing for key: ${key} in locale: ${locale}`);
    return key;
  }
  
  // 简单的参数替换
  if (params) {
    return Object.entries(params).reduce(
      (str, [paramKey, paramValue]) => 
        str.replace(`{${paramKey}}`, String(paramValue)),
      value
    );
  }
  
  return value;
}

/**
 * 富文本编辑器专用翻译函数
 */
export const richTextTranslations = {
  saveSuccess: () => getTranslation('richTextEditor.errors.saveSuccess'),
  saveFailed: () => getTranslation('richTextEditor.errors.saveFailed'),
};
