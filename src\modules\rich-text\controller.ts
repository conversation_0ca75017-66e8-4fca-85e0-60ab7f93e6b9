import type { Context } from 'hono';
import { createContentService } from './service';
import {
  createContentRequest,
  batchContentRequest,
  EntityTypeEnum,
  ContentTypeEnum,
} from './schema';
import { getDB } from '@/infrastructure';
import { jsonSuccess, jsonError } from '@/utils/errorResponse';
import { validationError } from '@/utils/errorResponse';
import { recordLog } from '@/utils/auditLog';

/**
 * 获取实体的所有内容
 * GET /{entityType}/{entityId}/content
 */
export async function getEntityContent(c: Context) {
  const entityType = c.req.param('entityType');
  const entityId = c.req.param('entityId');

  // 验证实体类型
  const entityTypeResult = EntityTypeEnum.safeParse(entityType);
  if (!entityTypeResult.success) {
    return jsonError(c, 10001, '无效的实体类型', 400);
  }

  if (!entityId) {
    return jsonError(c, 10001, '实体ID不能为空', 400);
  }

  try {
    const db = getDB(c);
    const contentService = createContentService(db);
    const content = await contentService.getEntityContent(
      entityTypeResult.data,
      entityId
    );

    return jsonSuccess(c, '获取内容成功', content);
  } catch (error) {
    console.error('获取内容失败:', error);
    return jsonError(c, 10003, '获取内容失败', 500);
  }
}

/**
 * 获取实体的特定类型内容
 * GET /{entityType}/{entityId}/content/{contentType}
 */
export async function getEntityContentByType(c: Context) {
  const entityType = c.req.param('entityType');
  const entityId = c.req.param('entityId');
  const contentType = c.req.param('contentType');

  // 验证参数
  const entityTypeResult = EntityTypeEnum.safeParse(entityType);
  const contentTypeResult = ContentTypeEnum.safeParse(contentType);

  if (!entityTypeResult.success) {
    return jsonError(c, 10001, '无效的实体类型', 400);
  }

  if (!contentTypeResult.success) {
    return jsonError(c, 10001, '无效的内容类型', 400);
  }

  if (!entityId) {
    return jsonError(c, 10001, '实体ID不能为空', 400);
  }

  try {
    const db = getDB(c);
    const contentService = createContentService(db);
    const content = await contentService.getEntityContentByType(
      entityTypeResult.data,
      entityId,
      contentTypeResult.data
    );

    if (content === null) {
      return jsonError(c, 10002, '内容不存在', 404);
    }

    return jsonSuccess(c, '获取内容成功', { content });
  } catch (error) {
    console.error('获取内容失败:', error);
    return jsonError(c, 10003, '获取内容失败', 500);
  }
}

/**
 * 创建或更新单个内容
 * POST /{entityType}/{entityId}/content
 */
export async function createOrUpdateContent(c: Context) {
  const entityType = c.req.param('entityType');
  const entityId = c.req.param('entityId');

  // 验证实体类型
  const entityTypeResult = EntityTypeEnum.safeParse(entityType);
  if (!entityTypeResult.success) {
    return jsonError(c, 10001, '无效的实体类型', 400);
  }

  if (!entityId) {
    return jsonError(c, 10001, '实体ID不能为空', 400);
  }

  try {
    const body = await c.req.json();
    const validatedData = createContentRequest.parse(body);

    // 确保实体类型和ID匹配
    if (
      validatedData.entity_type !== entityTypeResult.data ||
      validatedData.entity_id !== entityId
    ) {
      return jsonError(c, 10001, '请求参数与路径参数不匹配', 400);
    }

    const db = getDB(c);
    const contentService = createContentService(db);

    const result = await contentService.upsertContent(
      validatedData.entity_type,
      validatedData.entity_id,
      validatedData.content_type,
      validatedData.content
    );

    await recordLog(c, {
      action: 'UPSERT_CONTENT',
      targetType: 'content',
      targetId: result.id,
      meta: {
        entityType: validatedData.entity_type,
        entityId: validatedData.entity_id,
        contentType: validatedData.content_type,
      },
    });

    return jsonSuccess(c, '内容保存成功', result, 201);
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return validationError(c, { error: error.message });
    }
    console.error('保存内容失败:', error);
    return jsonError(c, 10003, '保存内容失败', 500);
  }
}

/**
 * 批量更新实体的所有内容
 * PUT /{entityType}/{entityId}/content
 */
export async function batchUpdateContent(c: Context) {
  const entityType = c.req.param('entityType');
  const entityId = c.req.param('entityId');

  // 验证实体类型
  const entityTypeResult = EntityTypeEnum.safeParse(entityType);
  if (!entityTypeResult.success) {
    return jsonError(c, 10001, '无效的实体类型', 400);
  }

  if (!entityId) {
    return jsonError(c, 10001, '实体ID不能为空', 400);
  }

  try {
    const body = await c.req.json();
    const validatedData = batchContentRequest.parse(body);

    const db = getDB(c);
    const contentService = createContentService(db);

    const result = await contentService.batchUpdateContent(
      entityTypeResult.data,
      entityId,
      validatedData
    );

    await recordLog(c, {
      action: 'BATCH_UPDATE_CONTENT',
      targetType: 'content',
      targetId: entityId,
      meta: {
        entityType: entityTypeResult.data,
        entityId,
        contentTypes: Object.keys(validatedData),
      },
    });

    return jsonSuccess(c, '批量更新成功', result);
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      return validationError(c, { error: error.message });
    }
    console.error('批量更新失败:', error);
    return jsonError(c, 10003, '批量更新失败', 500);
  }
}

/**
 * 删除实体的所有内容
 * DELETE /{entityType}/{entityId}/content
 */
export async function deleteEntityContent(c: Context) {
  const entityType = c.req.param('entityType');
  const entityId = c.req.param('entityId');

  // 验证实体类型
  const entityTypeResult = EntityTypeEnum.safeParse(entityType);
  if (!entityTypeResult.success) {
    return jsonError(c, 10001, '无效的实体类型', 400);
  }

  if (!entityId) {
    return jsonError(c, 10001, '实体ID不能为空', 400);
  }

  try {
    const db = getDB(c);
    const contentService = createContentService(db);

    await contentService.deleteEntityContent(entityTypeResult.data, entityId);

    await recordLog(c, {
      action: 'DELETE_ENTITY_CONTENT',
      targetType: 'content',
      targetId: entityId,
      meta: {
        entityType: entityTypeResult.data,
        entityId,
      },
    });

    return jsonSuccess(c, '删除成功');
  } catch (error) {
    console.error('删除内容失败:', error);
    return jsonError(c, 10003, '删除内容失败', 500);
  }
}

/**
 * 富文本编辑器图片上传
 * POST /api/upload/images
 */
export async function uploadImage(c: Context) {
  try {
    const formData = await c.req.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return jsonError(c, 10001, '图片文件不能为空', 400);
    }

    const db = getDB(c);
    const r2 = c.env.R2;
    const contentService = createContentService(db);

    const result = await contentService.uploadImage(file, r2);

    await recordLog(c, {
      action: 'UPLOAD_CONTENT_IMAGE',
      targetType: 'image',
      targetId: result.url,
      meta: {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
      },
    });

    return jsonSuccess(c, '图片上传成功', result);
  } catch (error: any) {
    console.error('图片上传失败:', error);

    // 如果是验证错误，返回400
    if (error.isValidationError) {
      return jsonError(c, 10001, error.message, 400);
    }

    return jsonError(c, 10003, '图片上传失败', 500);
  }
}
