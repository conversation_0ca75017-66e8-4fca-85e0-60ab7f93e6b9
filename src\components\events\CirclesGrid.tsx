import { VirtuosoGrid } from "react-virtuoso"
import { useMemo, useRef, useEffect, useState } from "react"

import CircleTextCard from "@/components/circle-text-card"
import { TransformedCircle } from "@/app/events/[id]/utils"
import { useBookmarkManager } from "@/hooks/useBookmark"

interface CirclesGridProps {
  data: TransformedCircle[]
}

export default function CirclesGrid({ data }: CirclesGridProps) {
  // 使用收藏状态管理器
  const { isBookmarked, bookmarkedIds } = useBookmarkManager();

  // 使用 ref 保存稳定的排序列表，避免收藏状态变化时重新排序
  const stableSortedCircles = useRef<TransformedCircle[]>([]);
  const mountTime = useRef(Date.now());
  const [, forceRender] = useState({});

  // 使用 ref 保存最新的状态，避免闭包陷阱
  const latestData = useRef(data);
  const latestBookmarkedIds = useRef(bookmarkedIds);

  // 更新 ref 中的最新值
  latestData.current = data;
  latestBookmarkedIds.current = bookmarkedIds;

  // 排序函数 - 使用传入的参数而不是闭包中的值
  const sortCircles = (circleList: TransformedCircle[], bookmarkIds: Set<string>) => {
    return [...circleList].sort((a, b) => {
      const aBookmarked = bookmarkIds.has(a.circle_id || a.id);
      const bBookmarked = bookmarkIds.has(b.circle_id || b.id);

      // 已收藏的排在前面
      if (aBookmarked && !bBookmarked) return -1;
      if (!aBookmarked && bBookmarked) return 1;

      // 其他情况保持原有顺序
      return 0;
    });
  };

  // 智能排序：只在初始化期间允许排序，避免用户操作时重排序
  useEffect(() => {
    const timeSinceMount = Date.now() - mountTime.current;

    // 只在组件挂载后的前3秒内允许排序（初始化期间）
    // 这样可以处理数据异步加载的情况，但避免用户操作时的重排序
    if (timeSinceMount < 3000 && data.length > 0) {
      stableSortedCircles.current = sortCircles(data, bookmarkedIds);
      forceRender({}); // 强制重新渲染
    }

    // 数据长度变化时也要排序（加载更多数据）
    else if (timeSinceMount >= 3000 && data.length !== stableSortedCircles.current.length) {
      stableSortedCircles.current = sortCircles(data, bookmarkedIds);
      forceRender({}); // 强制重新渲染
    }
  }, [data, bookmarkedIds]); // 依赖两者，但用时间限制避免用户操作时重排序

  // 监听页面可见性变化，标签页切换回来时重新排序
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && latestData.current.length > 0) {
        // 使用最新的状态进行排序
        stableSortedCircles.current = sortCircles(latestData.current, latestBookmarkedIds.current);
        forceRender({}); // 强制重新渲染
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []); // 空依赖数组，避免重复绑定事件

  // 使用稳定的排序列表
  const sortedCircles = stableSortedCircles.current.length > 0
    ? stableSortedCircles.current
    : data;

  return (
    <div data-testid="circles-grid">
      <VirtuosoGrid
        data={sortedCircles}
        style={{ height: "70vh" }}
        listClassName="w-full grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4"
        itemClassName="flex"
        overscan={400}
        itemContent={(index, circle) => (
          <CircleTextCard
            key={`${circle.circle_id}-${circle.booth_id}`}
            data={circle}
            isBookmarked={isBookmarked(circle.circle_id || circle.id)}
          />
        )}
      />
    </div>
  )
}