'use client'

import { useState } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

import { cn } from '@/lib/utils'
import { getValidImageUrl } from '@/lib/image-utils'
import { EventImageDisplay } from './EventImageDisplay'
import type { EventPosterProps } from './types'

/**
 * 事件海报组件
 *
 * 功能：
 * - 显示事件海报图片
 * - 支持新的图片 API 和传统 image_url
 * - 处理图片加载失败的情况
 * - 提供加载状态和错误状态
 * - 响应式设计
 */
export default function EventPoster({
  imageUrl,
  eventName,
  eventId,
  className
}: EventPosterProps) {
  const t = useTranslations('EventHeader')
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  const handleImageError = () => {
    setImageError(true)
    setImageLoading(false)
  }

  const handleImageLoad = () => {
    setImageLoading(false)
  }

  // 如果有 eventId，优先使用新的图片 API
  if (eventId) {
    return (
      <div className={cn(
        "relative w-full",
        className
      )}>
        <div className="aspect-[4/3] w-full">
          <EventImageDisplay
            eventId={eventId}
            imageType="poster"
            variant="medium" // 临时改为 medium，因为数据库中只有 medium 变体
            alt={eventName ? `${eventName} - ${t('imageAlt')}` : t('imageAlt')}
            className="rounded-lg shadow-lg w-full h-full"
          />
        </div>
      </div>
    )
  }

  // 回退到传统的 image_url 方式
  return (
    <div className={cn(
      "relative w-full",
      className
    )}>
      <div className="aspect-[4/3] w-full relative">
        {/* 加载状态骨架屏 */}
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-lg" />
        )}

        {/* 图片 */}
        <Image
          src={imageError ? "/next.svg" : getValidImageUrl(imageUrl)}
          alt={eventName ? `${eventName} - ${t('imageAlt')}` : t('imageAlt')}
          fill
          className={cn(
            "rounded-lg object-cover shadow-lg transition-opacity duration-300",
            imageLoading ? "opacity-0" : "opacity-100"
          )}
          onError={handleImageError}
          onLoad={handleImageLoad}
          priority
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
        />

        {/* 错误状态覆盖层 */}
        {imageError && imageUrl && (
          <div className="absolute inset-0 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500 p-4">
              <div className="text-2xl mb-2">🖼️</div>
              <p className="text-sm">图片加载失败</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
