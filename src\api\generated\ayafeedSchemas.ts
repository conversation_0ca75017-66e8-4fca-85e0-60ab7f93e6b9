/**
 * Generated by @openapi-codegen
 *
 * @version 0.4.2.5
 */
/**
 * 业务错误码
 */
export type ErrorCodes =
  | 10001
  | 10002
  | 10003
  | 20001
  | 20002
  | 30001
  | 30002
  | 40001
  | 40002
  | 40003
  | 50001
  | 50002
  | 60001
  | 60002
  | 60003;

export type ErrorResponse = {
  code: ErrorCodes;
  message: string;
  detail?: void | null;
  requestId: string;
};

export type AuthCredentials = {
  /**
   * @minLength 3
   * @example alice
   */
  username: string;
  /**
   * @minLength 6
   * @example secret123
   */
  password: string;
};

export type PaginatedResult = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    created_at?: string;
    updated_at?: string;
  }[];
};

export type SuccessResponse = {
  code: 0;
  message: string;
  data?: void | null;
};
