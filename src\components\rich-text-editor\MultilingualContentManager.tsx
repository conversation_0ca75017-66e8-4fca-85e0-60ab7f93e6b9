'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import * as Tabs from "@radix-ui/react-tabs";
import { FileText, Star, MapPin, Bell, Globe, Save, RotateCcw, Loader2 } from 'lucide-react';
import { cn } from "@/lib/utils";
import { RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription, RadixCardContent } from '@/components/ui/radix-components';
import { Button } from '@/components/ui/button';

import { RichTextEditor } from './RichTextEditor';
import { useMultilingualContent, type Locale } from '@/hooks/useMultilingualContent';
import { type EntityType, type ContentType } from '@/lib/api/content';

export interface MultilingualContentManagerProps {
  entityType: EntityType;
  entityId: string;
  className?: string;
}

// 支持的语言配置
const SUPPORTED_LOCALES: { code: Locale; name: string; flag: string }[] = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
];

function MultilingualContentManager({
  entityType,
  entityId,
  className = '',
}: MultilingualContentManagerProps) {
  const t = useTranslations('richTextEditor');

  const [activeContentType, setActiveContentType] = useState<ContentType>('introduction');

  // 使用多语言内容管理Hook
  const {
    selectedLocale,
    isLoading,
    isSaving,
    getCurrentContent,
    updateContent,
    loadContentForLocale,
    switchLocale,
    saveSingle,
    saveAll,
    resetContent,
    hasUnsavedChanges,
    loadedLocales,
  } = useMultilingualContent({
    entityType,
    entityId,
    // 不使用 currentLocale，让 hook 使用默认的 'zh'
    // 避免因为全局语言切换导致 hook 重新初始化
  });

  // 处理语言切换
  const handleLocaleChange = async (newLocale: Locale) => {
    try {
      await switchLocale(newLocale);
    } catch (error) {
      console.error('❌ MultilingualContentManager 语言切换失败:', error);
      // 静默处理语言切换错误，不显示 toast
      // 语言切换失败通常是因为该语言还没有内容，这是正常情况
    }
  };

  // 内容类型配置
  const CONTENT_TYPES = [
    {
      key: 'introduction' as const,
      label: t('contentTypes.introduction'),
      placeholder: t('placeholders.introduction'),
      icon: FileText,
    },
    {
      key: 'highlights' as const,
      label: t('contentTypes.highlights'),
      placeholder: t('placeholders.highlights'),
      icon: Star,
    },
    {
      key: 'guide' as const,
      label: t('contentTypes.guide'),
      placeholder: t('placeholders.guide'),
      icon: MapPin,
    },
    {
      key: 'notices' as const,
      label: t('contentTypes.notices'),
      placeholder: t('placeholders.notices'),
      icon: Bell,
    },
  ];

  // 处理内容变更
  const handleContentChange = (value: string) => {
    updateContent(activeContentType, value, selectedLocale);
  };

  // 处理保存操作
  const handleSave = async () => {
    try {
      await saveSingle(activeContentType);
    } catch (error) {
      console.error('Failed to save content:', error);
    }
  };

  const handleSaveAll = async () => {
    try {
      await saveAll();
    } catch (error) {
      console.error('Failed to save all content:', error);
    }
  };

  const handleReset = async () => {
    try {
      await resetContent();
    } catch (error) {
      console.error('Failed to reset content:', error);
    }
  };



  return (
    <div className={cn("space-y-6", className)}>
      <RadixCard>
        <RadixCardHeader>
          <RadixCardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {t('title')}
          </RadixCardTitle>
          <RadixCardDescription>
            {t('description')}
          </RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent className="space-y-6">
          {/* 语言选择器 */}
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium">{t('language.select')}:</span>
            <div className="flex gap-2">
              {SUPPORTED_LOCALES.map((locale) => (
                <button
                  key={locale.code}
                  type="button"
                  onClick={() => handleLocaleChange(locale.code)}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors",
                    selectedLocale === locale.code
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted hover:bg-muted/80"
                  )}
                >
                  <span>{locale.flag}</span>
                  <span>{locale.name}</span>
                  {!loadedLocales.has(locale.code) && (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* 内容类型标签页 */}
          <Tabs.Root value={activeContentType} onValueChange={(value) => setActiveContentType(value as ContentType)}>
            <Tabs.List className="grid w-full grid-cols-4 h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
              {CONTENT_TYPES.map((contentType) => {
                const Icon = contentType.icon;
                return (
                  <Tabs.Trigger
                    key={contentType.key}
                    value={contentType.key}
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {contentType.label}
                  </Tabs.Trigger>
                );
              })}
            </Tabs.List>

            {CONTENT_TYPES.map((contentType) => (
              <Tabs.Content key={contentType.key} value={contentType.key} className="mt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold flex items-center gap-2">
                        <contentType.icon className="h-5 w-5" />
                        {contentType.label}
                        <span className="text-sm text-muted-foreground">
                          ({SUPPORTED_LOCALES.find(l => l.code === selectedLocale)?.name})
                        </span>
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {contentType.placeholder}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        onClick={handleSave}
                        disabled={isSaving}
                        size="sm"
                        variant="default"
                        className="flex items-center gap-2"
                      >
                        {isSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4" />
                        )}
                        保存当前
                      </Button>

                      <Button
                        type="button"
                        onClick={handleSaveAll}
                        disabled={isSaving}
                        size="sm"
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        {isSaving ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4" />
                        )}
                        保存全部
                      </Button>

                      <Button
                        type="button"
                        onClick={handleReset}
                        disabled={isSaving}
                        size="sm"
                        variant="ghost"
                        className="flex items-center gap-2"
                      >
                        <RotateCcw className="h-4 w-4" />
                        重置
                      </Button>

                      {hasUnsavedChanges(selectedLocale, contentType.key) && (
                        <span className="text-sm text-amber-600 font-medium">
                          有未保存的更改
                        </span>
                      )}
                    </div>
                  </div>

                  <RichTextEditor
                    key={`${selectedLocale}-${contentType.key}`}
                    content={getCurrentContent(contentType.key, selectedLocale)}
                    onChange={handleContentChange}
                    placeholder={contentType.placeholder}
                    className="min-h-[400px]"
                  />
                </div>
              </Tabs.Content>
            ))}
          </Tabs.Root>
        </RadixCardContent>
      </RadixCard>
    </div>
  );
}

export { MultilingualContentManager };
export default MultilingualContentManager;
