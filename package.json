{"name": "ayafeed", "version": "0.2.1", "private": true, "scripts": {"dev": "next dev", "dev_old": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --ext .ts,.tsx --fix --max-warnings=0", "test": "vitest", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "analyze": "ANALYZE=true next build", "typecheck": "tsc --noEmit", "gen:type": "openapi-typescript openapi.json -o src/types/api-types.d.ts", "gen:rq": "openapi-codegen gen ayafeed --config scripts/openapi-codegen.config.ts && tsx scripts/patch-locale-support.ts", "gen:api": "pnpm run gen:type && pnpm run gen:rq", "sync:api": "pnpm gen:api && pnpm gen:rq && git diff --name-only --exit-code src/api/generated/ayafeedFetcher.ts", "prepare": "husky", "gen:rq:raw": "openapi-codegen gen ayafeed --config scripts/openapi-codegen.config.ts", "docs:dev": "cd website && docusaurus start", "docs:build": "cd website && docusaurus build", "docs:serve": "cd website && docusaurus serve", "docs:clear": "cd website && docusaurus clear", "docs:deploy": "cd website && docusaurus deploy"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tiptap/extension-bullet-list": "^3.0.7", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-list-item": "^3.0.7", "@tiptap/extension-ordered-list": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@types/minimist": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "isomorphic-dompurify": "^2.26.0", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "lucide-react": "^0.515.0", "minimist": "^1.2.8", "motion": "^12.23.12", "next": "15.3.4", "next-intl": "^4.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-leaflet": "^5.0.0", "react-virtuoso": "^4.13.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@docusaurus/core": "^3.8.1", "@docusaurus/module-type-aliases": "^3.8.1", "@docusaurus/preset-classic": "^3.8.1", "@docusaurus/theme-mermaid": "^3.8.1", "@docusaurus/tsconfig": "^3.8.1", "@docusaurus/types": "^3.8.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@next/bundle-analyzer": "^15.4.2", "@next/eslint-plugin-next": "^15.4.2", "@openapi-codegen/cli": "3.1.0", "@openapi-codegen/typescript": "11.0.1", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@turf/helpers": "^7.2.0", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.20", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^3.2.4", "depcheck": "^1.4.7", "eslint": "^9.31.0", "eslint-config-next": "15.3.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "msw": "^2.10.4", "openapi-typescript": "^7.8.0", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix"}, "vitest": {"coverage": {"reporter": ["text", "json", "lcov"], "branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "pnpm": {"overrides": {"got-fetch": "^6.0.2", "got": "^14.0.0"}, "onlyBuiltDependencies": ["@swc/core", "@tailwindcss/oxide", "esbuild", "got-fetch", "msgpackr-extract", "msw", "sharp", "unrs-resolver", "workerd"]}}