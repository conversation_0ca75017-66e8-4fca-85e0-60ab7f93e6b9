"use client";

import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Menu, Search, Calendar, Users, Activity, Home, Heart } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { RadixButton, RadixInput } from "@/components/ui/radix-components";
import { useState } from "react";

import LanguageSwitcher from '@/components/language-switcher';
import UserMenu from '@/components/user-menu';
import { SEARCH_CONFIG, placeholderSearch } from './navbar/search-config';

// 导航项配置 - 优化信息架构
const navigationItems = [
  { href: "/", label: "首页", icon: "Home" },
  { href: "/events", label: "展会", icon: "Calendar" },
  { href: "/circles", label: "社团", icon: "Users" },
  { href: "/bookmarks", label: "收藏", icon: "Heart" },
  { href: "/feed", label: "动态", icon: "Activity" },
] as const;

// 管理员专用导航项
const adminNavigationItems = [
  { href: "/admin", label: "管理后台", icon: "Settings" },
] as const;

// 导航链接组件
interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

function NavLink({ href, children, className, onClick }: NavLinkProps) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={cn(
        "text-sm font-medium transition-colors outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 rounded-sm",
        className
      )}
    >
      {children}
    </Link>
  );
}

// 移动端菜单项组件
function MobileMenuItem({ href, children }: { href: string; children: React.ReactNode }) {
  return (
    <DropdownMenu.Item asChild>
      <NavLink
        href={href}
        className="flex items-center px-4 py-3 rounded-lg text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 focus:bg-gradient-to-r focus:from-blue-50 focus:to-purple-50 dark:focus:from-blue-950 dark:focus:to-purple-950 cursor-pointer transition-all duration-300 font-medium"
      >
        {children}
      </NavLink>
    </DropdownMenu.Item>
  );
}

// 搜索组件 - 使用占位符逻辑
function SearchBar() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // 如果搜索功能被禁用，不渲染组件
  if (!SEARCH_CONFIG.enabled) {
    return null;
  }

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);

    try {
      console.log('🔍 搜索查询:', searchQuery.trim());

      if (SEARCH_CONFIG.usePlaceholder) {
        // 使用占位符搜索逻辑
        const searchResult = await placeholderSearch(searchQuery.trim());

        const resultText = searchResult.results.length > 0
          ? `找到 ${searchResult.total} 个相关结果:\n\n${searchResult.results.map(r => r.title).join('\n')}`
          : '未找到相关结果';

        alert(`🔍 搜索结果 (演示)\n查询: "${searchQuery.trim()}"\n\n${resultText}\n\n💡 提示: 搜索功能正在开发中，这只是演示效果`);
      } else {
        // TODO: 调用真实的搜索 API
        // const result = await realSearch({ query: searchQuery.trim() });
        alert('真实搜索 API 尚未实现');
      }

      // 清空搜索框
      setSearchQuery("");

    } catch (error) {
      console.error('搜索错误:', error);
      alert('搜索功能暂时不可用');
    } finally {
      setIsSearching(false);
    }
  };

  const displayClass = SEARCH_CONFIG.showMobileSearch
    ? "flex items-center"
    : "hidden md:flex items-center";

  return (
    <form onSubmit={handleSearch} className={displayClass}>
      <div className="relative group">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-500 dark:text-slate-400 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400 transition-colors duration-300" />
        <RadixInput
          type="search"
          placeholder={SEARCH_CONFIG.placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          disabled={isSearching}
          className={`pl-10 pr-12 py-2.5 ${SEARCH_CONFIG.maxWidth} bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 disabled:opacity-50 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 backdrop-blur-sm`}
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin h-4 w-4 border-2 border-blue-500/30 border-t-blue-500 rounded-full"></div>
          </div>
        )}
      </div>
    </form>
  );
}

// Logo 组件 - 现代艺术设计
function Logo() {
  return (
    <NavLink
      href="/"
      className="flex items-center gap-3 text-slate-900 dark:text-white hover:text-slate-700 dark:hover:text-white/90 transition-all duration-300 group"
    >
      <div className="relative w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:shadow-purple-500/30 group-hover:scale-105 transition-all duration-300">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-600 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <Activity className="relative h-5 w-5 text-white group-hover:scale-110 transition-transform duration-300" />
      </div>
      <span className="text-xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
        Ayafeed
      </span>
    </NavLink>
  );
}

// 图标映射
const iconMap = {
  Home,
  Calendar,
  Users,
  Heart,
  Activity,
} as const;

// 桌面端导航组件 - 现代艺术风格
function DesktopNavigation() {
  return (
    <div className="hidden gap-1 md:flex">
      {navigationItems.map(({ href, label, icon }) => {
        const IconComponent = iconMap[icon as keyof typeof iconMap];
        return (
          <NavLink
            key={href}
            href={href}
            className="flex items-center gap-2 px-4 py-2.5 rounded-xl text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 transition-all duration-300 font-medium text-sm relative group backdrop-blur-sm"
          >
            <div className="relative">
              <IconComponent className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
            </div>
            {label}
            <div className="absolute inset-x-2 bottom-1 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-full"></div>
          </NavLink>
        );
      })}
    </div>
  );
}

// 移动端菜单组件
function MobileMenu() {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <RadixButton
          variant="ghost"
          size="icon"
          className="md:hidden text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 rounded-xl transition-all duration-300"
          aria-label="打开菜单"
        >
          <Menu className="h-5 w-5" />
        </RadixButton>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="start"
          className="min-w-[220px] bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 rounded-xl p-2 shadow-xl shadow-slate-200/20 dark:shadow-slate-900/20 md:hidden z-50"
          sideOffset={5}
        >
          {navigationItems.map(({ href, label }) => (
            <MobileMenuItem key={href} href={href}>
              {label}
            </MobileMenuItem>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}

// 右侧操作区组件
function NavActions() {
  return (
    <div className="flex items-center gap-3">
      <LanguageSwitcher />
      <MobileMenu />
      <UserMenu />
    </div>
  );
}

export default function Navbar() {
  return (
    <nav className="sticky top-0 z-50 w-full border-b border-slate-200/20 dark:border-slate-700/30 bg-gradient-to-r from-white/95 via-slate-50/95 to-white/95 dark:from-slate-900/95 dark:via-slate-800/95 dark:to-slate-900/95 backdrop-blur-xl supports-backdrop-blur:bg-white/90 dark:supports-backdrop-blur:bg-slate-900/90 shadow-lg shadow-slate-200/20 dark:shadow-slate-900/20">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/5 to-purple-600/5 rounded-full blur-3xl"></div>
        <div className="absolute -top-20 -left-20 w-40 h-40 bg-gradient-to-br from-purple-400/5 to-pink-600/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* 左侧：Logo */}
          <div className="flex-shrink-0">
            <Logo />
          </div>

          {/* 中间：搜索栏和导航 */}
          <div className="flex-1 flex items-center justify-center gap-6">
            <div className="max-w-2xl">
              <SearchBar />
            </div>
            <DesktopNavigation />
          </div>

          {/* 右侧：操作菜单 */}
          <div className="flex-shrink-0">
            <NavActions />
          </div>
        </div>
      </div>
    </nav>
  );
}