import { fireEvent, vi } from "@testing-library/react";
import { expect, test } from "vitest";

import FilterBar from "@/components/events/FilterBar";
import { renderWithProviders, screen } from "@test/test-utils";

test("keyword input triggers setKeyword", () => {
  const setKeyword = vi.fn();
  renderWithProviders(
    <FilterBar
      keyword=""
      setKeyword={setKeyword}
    />
  );

  fireEvent.change(screen.getByPlaceholderText(/搜索/), {
    target: { value: "abc" },
  });
  expect(setKeyword).toHaveBeenCalledWith("abc");
});