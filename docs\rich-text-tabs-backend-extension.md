# 富文本编辑器标签页管理 - 后端扩展文档

> 📝 **版本**: v1.0.0
> 🕒 **更新时间**: 2025-01-02
> 👥 **目标读者**: 后端开发者
> 🎯 **功能**: 为富文本编辑器添加动态标签页管理功能

## 📋 目录

- [概述](#概述)
- [数据库设计](#数据库设计)
- [API 设计](#api-设计)
- [服务层实现](#服务层实现)
- [控制器实现](#控制器实现)
- [实现步骤](#实现步骤)
- [安全考虑](#安全考虑)
- [测试策略](#测试策略)

## 概述

### 功能目标

为富文本编辑器添加动态标签页管理功能，允许管理员通过后台界面：
- 添加新的内容类型标签页
- 删除不需要的标签页
- 修改标签页配置（名称、图标、排序等）
- 启用/禁用特定标签页

### 技术要求

- **向后兼容**: 保持现有 API 的兼容性
- **权限控制**: 仅 admin/editor 角色可管理
- **数据完整性**: 确保删除标签页时处理关联数据
- **性能优化**: 支持缓存和批量操作

## 数据库设计

### 新增表结构

```sql
-- 标签页配置表
CREATE TABLE content_type_configs (
  id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
  key TEXT NOT NULL UNIQUE,           -- 标签页唯一标识 (如: 'introduction')
  label TEXT NOT NULL,                -- 显示名称 (如: '介绍')
  icon TEXT DEFAULT 'FileText',       -- 图标名称 (Lucide 图标)
  sort_order INTEGER DEFAULT 0,      -- 排序顺序
  is_active BOOLEAN DEFAULT true,    -- 是否启用
  is_system BOOLEAN DEFAULT false,   -- 是否为系统预设 (不可删除)
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  updated_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now')),
  
  -- 约束
  CHECK(length(key) >= 2 AND length(key) <= 50),
  CHECK(length(label) >= 1 AND length(label) <= 100),
  CHECK(sort_order >= 0)
);

-- 创建索引
CREATE INDEX idx_content_type_configs_active_sort ON content_type_configs(is_active, sort_order);
CREATE INDEX idx_content_type_configs_key ON content_type_configs(key);
```

### 数据迁移脚本

```sql
-- 迁移现有的内容类型为配置数据
INSERT INTO content_type_configs (key, label, placeholder, icon, sort_order, is_system) VALUES
('introduction', '介绍', '请输入介绍内容...', 'FileText', 1, true),
('highlights', '亮点', '请输入亮点内容...', 'Star', 2, true),
('guide', '指南', '请输入指南内容...', 'MapPin', 3, true),
('notices', '公告', '请输入公告内容...', 'Bell', 4, true);

-- 移除原有的 CHECK 约束 (如果需要)
-- 注意: SQLite 不支持直接删除约束，需要重建表
```

### 修改现有表

```sql
-- 为 rich_text_contents 表添加外键关联 (可选)
-- 注意: 考虑到性能和复杂性，可以选择不添加外键，通过应用层保证一致性
```

## API 设计

### 路由结构

```
/admin/rich-text/content-types/
├── GET    /           # 获取所有标签页配置
├── POST   /           # 创建新标签页
├── GET    /:id        # 获取单个标签页配置
├── PUT    /:id        # 更新标签页配置
├── DELETE /:id        # 删除标签页
└── PUT    /reorder    # 批量更新排序
```

### Schema 定义

```typescript
// src/modules/rich-text/contentTypeSchema.ts
import { z } from '@hono/zod-openapi';

export const contentTypeConfigSchema = z.object({
  id: z.string().openapi({ example: 'uuid-123' }),
  key: z.string().min(2).max(50).openapi({ example: 'introduction' }),
  label: z.string().min(1).max(100).openapi({ example: '介绍' }),
  placeholder: z.string().optional().openapi({ example: '请输入介绍内容...' }),
  icon: z.string().default('FileText').openapi({ example: 'FileText' }),
  sort_order: z.number().int().min(0).openapi({ example: 1 }),
  is_active: z.boolean().default(true),
  is_system: z.boolean().default(false),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

export const createContentTypeConfigRequest = z.object({
  key: z.string().min(2).max(50),
  label: z.string().min(1).max(100),
  placeholder: z.string().optional(),
  icon: z.string().default('FileText'),
  sort_order: z.number().int().min(0).optional(),
  is_active: z.boolean().default(true),
});

export const updateContentTypeConfigRequest = createContentTypeConfigRequest.partial();

export const reorderContentTypesRequest = z.object({
  items: z.array(z.object({
    id: z.string(),
    sort_order: z.number().int().min(0),
  })),
});

export type ContentTypeConfig = z.infer<typeof contentTypeConfigSchema>;
export type CreateContentTypeConfigRequest = z.infer<typeof createContentTypeConfigRequest>;
export type UpdateContentTypeConfigRequest = z.infer<typeof updateContentTypeConfigRequest>;
```

### API 路由实现

```typescript
// src/modules/rich-text/contentTypeAdminRoutes.ts
import { OpenAPIHono, createRoute } from '@hono/zod-openapi';
import { 
  contentTypeConfigSchema,
  createContentTypeConfigRequest,
  updateContentTypeConfigRequest,
  reorderContentTypesRequest
} from './contentTypeSchema';
import * as contentTypeController from './contentTypeController';
import { HonoApp } from '@/types';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

const routes = new OpenAPIHono<HonoApp>();

// 获取所有标签页配置
const getContentTypesRoute = createRoute({
  method: 'get',
  path: '/',
  summary: '获取所有标签页配置',
  tags: ['Admin.RichText.ContentTypes'],
  responses: {
    200: {
      description: '标签页配置列表',
      content: {
        'application/json': {
          schema: z.array(contentTypeConfigSchema),
        },
      },
    },
  },
});

// 创建新标签页
const createContentTypeRoute = createRoute({
  method: 'post',
  path: '/',
  summary: '创建新标签页',
  tags: ['Admin.RichText.ContentTypes'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: createContentTypeConfigRequest,
        },
      },
    },
  },
  responses: {
    201: {
      description: '创建成功',
      content: {
        'application/json': {
          schema: contentTypeConfigSchema,
        },
      },
    },
    400: { description: '请求参数错误' },
    409: { description: 'key 已存在' },
  },
});

// 更新标签页配置
const updateContentTypeRoute = createRoute({
  method: 'put',
  path: '/{id}',
  summary: '更新标签页配置',
  tags: ['Admin.RichText.ContentTypes'],
  request: {
    params: z.object({
      id: z.string(),
    }),
    body: {
      content: {
        'application/json': {
          schema: updateContentTypeConfigRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '更新成功',
      content: {
        'application/json': {
          schema: contentTypeConfigSchema,
        },
      },
    },
    404: { description: '标签页不存在' },
    400: { description: '请求参数错误' },
  },
});

// 删除标签页
const deleteContentTypeRoute = createRoute({
  method: 'delete',
  path: '/{id}',
  summary: '删除标签页',
  tags: ['Admin.RichText.ContentTypes'],
  request: {
    params: z.object({
      id: z.string(),
    }),
  },
  responses: {
    200: { description: '删除成功' },
    404: { description: '标签页不存在' },
    400: { description: '系统预设标签页不可删除' },
  },
});

// 批量更新排序
const reorderContentTypesRoute = createRoute({
  method: 'put',
  path: '/reorder',
  summary: '批量更新标签页排序',
  tags: ['Admin.RichText.ContentTypes'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: reorderContentTypesRequest,
        },
      },
    },
  },
  responses: {
    200: { description: '排序更新成功' },
    400: { description: '请求参数错误' },
  },
});

// 注册路由
registerOpenApiRoute(routes, getContentTypesRoute, contentTypeController.getContentTypes);
registerOpenApiRoute(routes, createContentTypeRoute, contentTypeController.createContentType);
registerOpenApiRoute(routes, updateContentTypeRoute, contentTypeController.updateContentType);
registerOpenApiRoute(routes, deleteContentTypeRoute, contentTypeController.deleteContentType);
registerOpenApiRoute(routes, reorderContentTypesRoute, contentTypeController.reorderContentTypes);

export { routes as contentTypeAdminRoutes };

## 服务层实现

### Repository 层

```typescript
// src/modules/rich-text/contentTypeRepository.ts
import { ContentTypeConfig, CreateContentTypeConfigRequest, UpdateContentTypeConfigRequest } from './contentTypeSchema';

export interface ContentTypeRepository {
  findAll(): Promise<ContentTypeConfig[]>;
  findById(id: string): Promise<ContentTypeConfig | null>;
  findByKey(key: string): Promise<ContentTypeConfig | null>;
  create(data: CreateContentTypeConfigRequest): Promise<ContentTypeConfig>;
  update(id: string, data: UpdateContentTypeConfigRequest): Promise<ContentTypeConfig>;
  delete(id: string): Promise<void>;
  reorder(items: Array<{ id: string; sort_order: number }>): Promise<void>;
  findActiveOrderedTypes(): Promise<ContentTypeConfig[]>;
}

export function createContentTypeRepository(db: D1Database): ContentTypeRepository {
  return {
    async findAll(): Promise<ContentTypeConfig[]> {
      const stmt = db.prepare(`
        SELECT * FROM content_type_configs
        ORDER BY sort_order ASC, created_at ASC
      `);
      const result = await stmt.all();
      return result.results as ContentTypeConfig[];
    },

    async findById(id: string): Promise<ContentTypeConfig | null> {
      const stmt = db.prepare('SELECT * FROM content_type_configs WHERE id = ?');
      const result = await stmt.bind(id).first();
      return result as ContentTypeConfig | null;
    },

    async findByKey(key: string): Promise<ContentTypeConfig | null> {
      const stmt = db.prepare('SELECT * FROM content_type_configs WHERE key = ?');
      const result = await stmt.bind(key).first();
      return result as ContentTypeConfig | null;
    },

    async create(data: CreateContentTypeConfigRequest): Promise<ContentTypeConfig> {
      const id = crypto.randomUUID();
      const now = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO content_type_configs
        (id, key, label, placeholder, icon, sort_order, is_active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      await stmt.bind(
        id,
        data.key,
        data.label,
        data.placeholder || null,
        data.icon || 'FileText',
        data.sort_order || 0,
        data.is_active ?? true,
        now,
        now
      ).run();

      return {
        id,
        ...data,
        icon: data.icon || 'FileText',
        sort_order: data.sort_order || 0,
        is_active: data.is_active ?? true,
        is_system: false,
        created_at: now,
        updated_at: now,
      };
    },

    async update(id: string, data: UpdateContentTypeConfigRequest): Promise<ContentTypeConfig> {
      const existing = await this.findById(id);
      if (!existing) {
        throw new Error('Content type config not found');
      }

      const now = new Date().toISOString();
      const updates: string[] = [];
      const values: any[] = [];

      if (data.key !== undefined) {
        updates.push('key = ?');
        values.push(data.key);
      }
      if (data.label !== undefined) {
        updates.push('label = ?');
        values.push(data.label);
      }
      if (data.placeholder !== undefined) {
        updates.push('placeholder = ?');
        values.push(data.placeholder);
      }
      if (data.icon !== undefined) {
        updates.push('icon = ?');
        values.push(data.icon);
      }
      if (data.sort_order !== undefined) {
        updates.push('sort_order = ?');
        values.push(data.sort_order);
      }
      if (data.is_active !== undefined) {
        updates.push('is_active = ?');
        values.push(data.is_active);
      }

      updates.push('updated_at = ?');
      values.push(now);
      values.push(id);

      const stmt = db.prepare(`
        UPDATE content_type_configs
        SET ${updates.join(', ')}
        WHERE id = ?
      `);

      await stmt.bind(...values).run();

      return await this.findById(id) as ContentTypeConfig;
    },

    async delete(id: string): Promise<void> {
      const stmt = db.prepare('DELETE FROM content_type_configs WHERE id = ?');
      await stmt.bind(id).run();
    },

    async reorder(items: Array<{ id: string; sort_order: number }>): Promise<void> {
      const stmt = db.prepare(`
        UPDATE content_type_configs
        SET sort_order = ?, updated_at = ?
        WHERE id = ?
      `);

      const now = new Date().toISOString();

      for (const item of items) {
        await stmt.bind(item.sort_order, now, item.id).run();
      }
    },

    async findActiveOrderedTypes(): Promise<ContentTypeConfig[]> {
      const stmt = db.prepare(`
        SELECT * FROM content_type_configs
        WHERE is_active = true
        ORDER BY sort_order ASC, created_at ASC
      `);
      const result = await stmt.all();
      return result.results as ContentTypeConfig[];
    },
  };
}
```

### Service 层

```typescript
// src/modules/rich-text/contentTypeService.ts
import { ContentTypeRepository, createContentTypeRepository } from './contentTypeRepository';
import { ContentTypeConfig, CreateContentTypeConfigRequest, UpdateContentTypeConfigRequest } from './contentTypeSchema';

export class ContentTypeService {
  private repository: ContentTypeRepository;

  constructor(db: D1Database) {
    this.repository = createContentTypeRepository(db);
  }

  async getAllContentTypes(): Promise<ContentTypeConfig[]> {
    return await this.repository.findAll();
  }

  async getActiveContentTypes(): Promise<ContentTypeConfig[]> {
    return await this.repository.findActiveOrderedTypes();
  }

  async getContentTypeById(id: string): Promise<ContentTypeConfig | null> {
    return await this.repository.findById(id);
  }

  async createContentType(data: CreateContentTypeConfigRequest): Promise<ContentTypeConfig> {
    // 检查 key 是否已存在
    const existing = await this.repository.findByKey(data.key);
    if (existing) {
      throw new Error(`Content type with key '${data.key}' already exists`);
    }

    // 验证 key 格式 (只允许字母、数字、下划线、连字符)
    if (!/^[a-zA-Z0-9_-]+$/.test(data.key)) {
      throw new Error('Key can only contain letters, numbers, underscores, and hyphens');
    }

    return await this.repository.create(data);
  }

  async updateContentType(id: string, data: UpdateContentTypeConfigRequest): Promise<ContentTypeConfig> {
    const existing = await this.repository.findById(id);
    if (!existing) {
      throw new Error('Content type not found');
    }

    // 如果是系统预设类型，限制某些字段的修改
    if (existing.is_system && data.key && data.key !== existing.key) {
      throw new Error('Cannot change key of system content type');
    }

    // 如果更新 key，检查是否与其他记录冲突
    if (data.key && data.key !== existing.key) {
      const conflicting = await this.repository.findByKey(data.key);
      if (conflicting) {
        throw new Error(`Content type with key '${data.key}' already exists`);
      }

      // 验证 key 格式
      if (!/^[a-zA-Z0-9_-]+$/.test(data.key)) {
        throw new Error('Key can only contain letters, numbers, underscores, and hyphens');
      }
    }

    return await this.repository.update(id, data);
  }

  async deleteContentType(id: string): Promise<void> {
    const existing = await this.repository.findById(id);
    if (!existing) {
      throw new Error('Content type not found');
    }

    // 系统预设类型不可删除
    if (existing.is_system) {
      throw new Error('Cannot delete system content type');
    }

    // TODO: 检查是否有关联的内容，如果有则需要处理
    // 可以选择：1) 阻止删除 2) 级联删除 3) 将内容标记为孤立

    await this.repository.delete(id);
  }

  async reorderContentTypes(items: Array<{ id: string; sort_order: number }>): Promise<void> {
    // 验证所有 ID 都存在
    for (const item of items) {
      const existing = await this.repository.findById(item.id);
      if (!existing) {
        throw new Error(`Content type with id '${item.id}' not found`);
      }
    }

    await this.repository.reorder(items);
  }
}

export function createContentTypeService(db: D1Database): ContentTypeService {
  return new ContentTypeService(db);
}

## 控制器实现

```typescript
// src/modules/rich-text/contentTypeController.ts
import { Context } from 'hono';
import { createContentTypeService } from './contentTypeService';
import {
  createContentTypeConfigRequest,
  updateContentTypeConfigRequest,
  reorderContentTypesRequest
} from './contentTypeSchema';
import { getDB } from '@/infrastructure';
import { jsonSuccess, jsonError, validationError } from '@/utils/response';
import { recordLog } from '@/modules/log/service';

/**
 * 获取所有标签页配置
 * GET /admin/rich-text/content-types
 */
export async function getContentTypes(c: Context) {
  try {
    const db = getDB(c);
    const service = createContentTypeService(db);
    const contentTypes = await service.getAllContentTypes();

    return jsonSuccess(c, '获取标签页配置成功', contentTypes);
  } catch (error) {
    console.error('获取标签页配置失败:', error);
    return jsonError(c, 50001, '获取标签页配置失败', 500);
  }
}

/**
 * 创建新标签页
 * POST /admin/rich-text/content-types
 */
export async function createContentType(c: Context) {
  try {
    const body = await c.req.json();
    const validatedData = createContentTypeConfigRequest.parse(body);

    const db = getDB(c);
    const service = createContentTypeService(db);
    const result = await service.createContentType(validatedData);

    await recordLog(c, {
      action: 'CREATE_CONTENT_TYPE',
      targetType: 'content_type_config',
      targetId: result.id,
      meta: { key: result.key, label: result.label },
    });

    return jsonSuccess(c, '标签页创建成功', result, 201);
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'ZodError') {
        return validationError(c, { error: error.message });
      }
      if (error.message.includes('already exists')) {
        return jsonError(c, 40901, error.message, 409);
      }
      if (error.message.includes('Key can only contain')) {
        return jsonError(c, 40001, error.message, 400);
      }
    }
    console.error('创建标签页失败:', error);
    return jsonError(c, 50002, '创建标签页失败', 500);
  }
}

/**
 * 更新标签页配置
 * PUT /admin/rich-text/content-types/:id
 */
export async function updateContentType(c: Context) {
  const id = c.req.param('id');

  if (!id) {
    return jsonError(c, 40001, '标签页ID不能为空', 400);
  }

  try {
    const body = await c.req.json();
    const validatedData = updateContentTypeConfigRequest.parse(body);

    const db = getDB(c);
    const service = createContentTypeService(db);
    const result = await service.updateContentType(id, validatedData);

    await recordLog(c, {
      action: 'UPDATE_CONTENT_TYPE',
      targetType: 'content_type_config',
      targetId: id,
      meta: { key: result.key, label: result.label },
    });

    return jsonSuccess(c, '标签页更新成功', result);
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'ZodError') {
        return validationError(c, { error: error.message });
      }
      if (error.message === 'Content type not found') {
        return jsonError(c, 40401, '标签页不存在', 404);
      }
      if (error.message.includes('Cannot change key') || error.message.includes('already exists')) {
        return jsonError(c, 40001, error.message, 400);
      }
    }
    console.error('更新标签页失败:', error);
    return jsonError(c, 50003, '更新标签页失败', 500);
  }
}

/**
 * 删除标签页
 * DELETE /admin/rich-text/content-types/:id
 */
export async function deleteContentType(c: Context) {
  const id = c.req.param('id');

  if (!id) {
    return jsonError(c, 40001, '标签页ID不能为空', 400);
  }

  try {
    const db = getDB(c);
    const service = createContentTypeService(db);

    // 获取要删除的标签页信息用于日志
    const contentType = await service.getContentTypeById(id);
    if (!contentType) {
      return jsonError(c, 40401, '标签页不存在', 404);
    }

    await service.deleteContentType(id);

    await recordLog(c, {
      action: 'DELETE_CONTENT_TYPE',
      targetType: 'content_type_config',
      targetId: id,
      meta: { key: contentType.key, label: contentType.label },
    });

    return jsonSuccess(c, '标签页删除成功');
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === 'Content type not found') {
        return jsonError(c, 40401, '标签页不存在', 404);
      }
      if (error.message === 'Cannot delete system content type') {
        return jsonError(c, 40001, '系统预设标签页不可删除', 400);
      }
    }
    console.error('删除标签页失败:', error);
    return jsonError(c, 50004, '删除标签页失败', 500);
  }
}

/**
 * 批量更新标签页排序
 * PUT /admin/rich-text/content-types/reorder
 */
export async function reorderContentTypes(c: Context) {
  try {
    const body = await c.req.json();
    const validatedData = reorderContentTypesRequest.parse(body);

    const db = getDB(c);
    const service = createContentTypeService(db);
    await service.reorderContentTypes(validatedData.items);

    await recordLog(c, {
      action: 'REORDER_CONTENT_TYPES',
      targetType: 'content_type_config',
      targetId: 'batch',
      meta: { count: validatedData.items.length },
    });

    return jsonSuccess(c, '标签页排序更新成功');
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'ZodError') {
        return validationError(c, { error: error.message });
      }
      if (error.message.includes('not found')) {
        return jsonError(c, 40001, error.message, 400);
      }
    }
    console.error('更新标签页排序失败:', error);
    return jsonError(c, 50005, '更新标签页排序失败', 500);
  }
}
```

## 实现步骤

### 阶段 1: 数据库迁移 (1-2 天)

1. **创建迁移脚本**
   ```bash
   # 创建新的迁移文件
   touch db/migrations/20250102_add_content_type_configs.sql
   ```

2. **执行数据迁移**
   - 创建 `content_type_configs` 表
   - 插入默认的4个内容类型配置
   - 测试数据完整性

3. **更新 schema.sql**
   - 添加新表定义
   - 更新相关索引

### 阶段 2: 后端 API 开发 (2-3 天)

1. **创建基础文件结构**
   ```
   src/modules/rich-text/
   ├── contentTypeSchema.ts      # Schema 定义
   ├── contentTypeRepository.ts  # 数据访问层
   ├── contentTypeService.ts     # 业务逻辑层
   ├── contentTypeController.ts  # 控制器层
   └── contentTypeAdminRoutes.ts # 路由定义
   ```

2. **实现核心功能**
   - Repository 层：数据库操作
   - Service 层：业务逻辑和验证
   - Controller 层：HTTP 请求处理
   - Routes 层：API 路由定义

3. **集成到主应用**
   ```typescript
   // src/modules/admin/routes.ts
   import { contentTypeAdminRoutes } from '../rich-text/contentTypeAdminRoutes';

   routes.route('/rich-text/content-types', contentTypeAdminRoutes);
   ```

### 阶段 3: 权限和安全 (1 天)

1. **添加权限控制**
   ```typescript
   routes.use('/rich-text/content-types/*', roleGuard(['admin', 'editor']));
   ```

2. **输入验证和清理**
   - 使用 Zod 进行严格的输入验证
   - 防止 XSS 和注入攻击
   - 限制字段长度和格式

3. **操作日志记录**
   - 记录所有 CRUD 操作
   - 包含操作者信息和详细元数据

### 阶段 4: 测试和文档 (1-2 天)

1. **单元测试**
   - Repository 层测试
   - Service 层业务逻辑测试
   - Controller 层 HTTP 接口测试

2. **集成测试**
   - API 端到端测试
   - 权限控制测试
   - 数据一致性测试

3. **API 文档更新**
   - OpenAPI 规范自动生成
   - 示例请求和响应
   - 错误码说明

## 安全考虑

### 1. 输入验证

```typescript
// 严格的输入验证
const contentTypeConfigSchema = z.object({
  key: z.string()
    .min(2, '标识符至少2个字符')
    .max(50, '标识符最多50个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '标识符只能包含字母、数字、下划线和连字符'),
  label: z.string()
    .min(1, '显示名称不能为空')
    .max(100, '显示名称最多100个字符'),
  // ... 其他字段验证
});
```

### 2. 权限控制

```typescript
// 细粒度权限控制
routes.use('*', authMiddleware());
routes.use('/rich-text/content-types/*', roleGuard(['admin', 'editor']));

// 系统预设类型保护
if (existing.is_system && sensitiveOperation) {
  throw new Error('Cannot modify system content type');
}
```

### 3. 数据完整性

```typescript
// 删除前检查关联数据
async deleteContentType(id: string): Promise<void> {
  // 检查是否有关联的富文本内容
  const hasContent = await this.checkRelatedContent(id);
  if (hasContent) {
    throw new Error('Cannot delete content type with existing content');
  }

  await this.repository.delete(id);
}
```

### 4. 操作审计

```typescript
// 详细的操作日志
await recordLog(c, {
  action: 'DELETE_CONTENT_TYPE',
  targetType: 'content_type_config',
  targetId: id,
  meta: {
    key: contentType.key,
    label: contentType.label,
    had_content: hasRelatedContent
  },
});
```

## 测试策略

### 1. 单元测试

```typescript
// 测试 Service 层业务逻辑
describe('ContentTypeService', () => {
  test('should create content type with valid data', async () => {
    const service = createContentTypeService(mockDb);
    const result = await service.createContentType({
      key: 'test-type',
      label: '测试类型',
      icon: 'TestIcon',
    });

    expect(result.key).toBe('test-type');
    expect(result.label).toBe('测试类型');
  });

  test('should reject duplicate key', async () => {
    const service = createContentTypeService(mockDb);
    await expect(
      service.createContentType({ key: 'introduction', label: '重复' })
    ).rejects.toThrow('already exists');
  });
});
```

### 2. 集成测试

```typescript
// 测试完整的 API 流程
describe('Content Type Admin API', () => {
  test('should create, update, and delete content type', async () => {
    // 创建
    const createResponse = await request(app)
      .post('/admin/rich-text/content-types')
      .send({ key: 'test', label: '测试' })
      .expect(201);

    const id = createResponse.body.data.id;

    // 更新
    await request(app)
      .put(`/admin/rich-text/content-types/${id}`)
      .send({ label: '更新后的测试' })
      .expect(200);

    // 删除
    await request(app)
      .delete(`/admin/rich-text/content-types/${id}`)
      .expect(200);
  });
});
```

### 3. 性能测试

```typescript
// 测试批量操作性能
test('should handle bulk reorder efficiently', async () => {
  const items = Array.from({ length: 100 }, (_, i) => ({
    id: `id-${i}`,
    sort_order: i,
  }));

  const startTime = Date.now();
  await service.reorderContentTypes(items);
  const duration = Date.now() - startTime;

  expect(duration).toBeLessThan(1000); // 应在1秒内完成
});
```

---

## 📚 相关文档

- [富文本编辑器标签页管理 - 前端扩展文档](./rich-text-tabs-frontend-extension.md)
- [富文本模块前端对接文档](../富文本模块前端对接文档.md)
- [API 开发规范](./api-development-guidelines.md)
- [数据库迁移指南](./database-migration-guide.md)
```
```
