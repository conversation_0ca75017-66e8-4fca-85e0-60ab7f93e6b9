/**
 * 搜索功能配置文件
 * 
 * 这个文件包含了搜索功能的所有配置选项，
 * 包括占位符逻辑和将来的 API 集成准备
 */

export interface SearchConfig {
  // 基础配置
  enabled: boolean;           // 是否启用搜索功能
  showMobileSearch: boolean;  // 是否在移动端显示搜索
  
  // 占位符配置
  usePlaceholder: boolean;    // 是否使用占位符逻辑
  mockDelay: number;          // 模拟 API 延迟 (毫秒)
  
  // UI 配置
  placeholder: string;        // 搜索框占位符文本
  maxWidth: string;          // 搜索框最大宽度
  
  // 模拟数据
  mockResults: string[];     // 模拟搜索结果
}

// 当前搜索配置
export const SEARCH_CONFIG: SearchConfig = {
  // 基础配置
  enabled: true,              // 启用搜索功能展示
  showMobileSearch: false,    // 移动端暂时隐藏（可根据需要开启）
  
  // 占位符配置
  usePlaceholder: true,       // 使用占位符逻辑
  mockDelay: 800,            // 模拟真实 API 延迟
  
  // UI 配置
  placeholder: "搜索展会、社团...",
  maxWidth: "w-full",
  
  // 模拟搜索结果数据
  mockResults: [
    "Comiket 103 - 东京国际展示场",
    "博丽神社例大祭 - 东京 Big Sight", 
    "Comic1☆19 - 东京 Big Sight",
    "COMITIA - 东京 Big Sight",
    "砲雷撃戦 - 大田区産業プラザ",
    "社团: 上海アリス幻樂団",
    "社团: 黄昏フロンティア",
    "社团: Team Shanghai Alice",
    "社团: 東方Project",
    "社团: 艦隊これくしょん",
  ]
};

// 搜索 API 接口定义（为将来的实现做准备）
export interface SearchAPI {
  // 搜索请求参数
  query: string;
  type?: 'all' | 'events' | 'circles';
  page?: number;
  limit?: number;
}

export interface SearchResult {
  // 搜索结果项
  id: string;
  type: 'event' | 'circle';
  title: string;
  description?: string;
  url: string;
  imageUrl?: string;
  tags?: string[];
}

export interface SearchResponse {
  // 搜索响应
  results: SearchResult[];
  total: number;
  page: number;
  hasMore: boolean;
}

// 占位符搜索函数
export async function placeholderSearch(query: string): Promise<SearchResponse> {
  // 模拟 API 延迟
  await new Promise(resolve => setTimeout(resolve, SEARCH_CONFIG.mockDelay));
  
  // 模拟搜索逻辑
  const filteredResults = SEARCH_CONFIG.mockResults
    .filter(result => 
      result.toLowerCase().includes(query.toLowerCase()) ||
      query.toLowerCase().split(' ').some(term => 
        result.toLowerCase().includes(term)
      )
    )
    .slice(0, 5)
    .map((result, index) => ({
      id: `mock-${index}`,
      type: result.includes('社团') ? 'circle' as const : 'event' as const,
      title: result,
      description: `这是 "${result}" 的描述信息`,
      url: result.includes('社团') ? `/circles/mock-${index}` : `/events/mock-${index}`,
      tags: result.includes('社团') ? ['同人', '社团'] : ['展会', '活动']
    }));
  
  return {
    results: filteredResults,
    total: filteredResults.length,
    page: 1,
    hasMore: false
  };
}

// 将来的真实搜索函数接口
export async function realSearch(params: SearchAPI): Promise<SearchResponse> {
  // TODO: 实现真实的搜索 API 调用
  // 示例实现：
  /*
  const response = await fetch('/api/search', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    throw new Error('搜索请求失败');
  }
  
  return await response.json();
  */
  
  // 当前返回占位符结果
  return placeholderSearch(params.query);
}
