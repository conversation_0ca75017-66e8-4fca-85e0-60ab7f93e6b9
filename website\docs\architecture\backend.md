# API 集成规范

> 前端应用与后端 API 的集成规范和最佳实践。本文档基于当前代码仓库整理，后续新增接口请同步维护。

## 1. 基础封装

项目统一通过 `src/lib/http.ts` 封装 `fetch`：

- 自动拼接 `baseURL`（默认 `http://localhost:8787`，可通过 `NEXT_PUBLIC_API_URL` 覆盖，推荐在 `.env.local` 中设置）。
- 请求异常时抛出自定义 `ApiError`（含 `code`、`message`）。
- 返回值统一解析为 **JSON** 并按范型参数推断类型。

```ts
const res = await http<User>('/users/1');
```

:::tip
所有直接调用后端的模块都应依赖此方法，避免在组件中出现裸 `fetch`。
:::

## 2. 通用响应格式

后端已全面实施统一的“三段式”响应包装，所有接口（含错误）均遵循以下结构：

```jsonc
{
  "code": 0,          // 0 表示成功；>0 为业务错误码
  "data": { ... },    // 业务数据（失败时可省略或为空对象）
  "message": "OK"     // 人类可读信息
}
```

- 成功：`code = 0`，`data` 为具体内容。
- 失败：`code ≠ 0`，`message` 为错误描述；HTTP Status 与业务场景保持一致（如 422 / 400 / 401 / 403 / 409 / 500）。

### 2.1 错误码区段

| 区段  | 说明         | 示例                                  |
| ----- | ------------ | ------------------------------------- |
| 1xxxx | 认证 / 登录  | **10001** `InvalidUsernameOrPassword` |
| 2xxxx | 权限 / 角色  | **20001** `Forbidden`                 |
| 3xxxx | 资源状态冲突 | **30004** `AlreadyProcessed`          |
| 4xxxx | 参数校验     | **40002** `MissingField`              |
| 5xxxx | 服务器内部   | **50000** `UnknownError`              |

完整映射见 `src/errors.ts`，前端可根据 `code` 精确分支处理。

## 2.2 成功响应 message 约定

后端在**写操作（POST / PUT / DELETE）**完成后，除返回常规 `code / data / message` 字段外，还会在 **HTTP Header** 附加 `X-Success-Message`，其值与响应体中的 `message` 字段相同，方便前端拦截全局提示。

后端统一通过 `jsonSuccess<T>(data, message?)` Helper 生成如下结构（TypeScript 伪代码）：

```ts
function jsonSuccess<T>(data: T, message = 'OK') {
  return c.json(
    {
      code: 0,
      data,
      message,
    },
    200,
    {
      'X-Success-Message': message,
    }
  );
}
```

### 2.2.1 常用成功提示文案

| 模块      | 方法   | 路径示例                       | 场景     | message                     |
| --------- | ------ | ------------------------------ | -------- | --------------------------- |
| Circles   | POST   | `/admin/circles`               | 新建社团 | "社团创建成功"              |
| Circles   | PUT    | `/admin/circles/:id`           | 编辑社团 | "社团已保存"                |
| Circles   | DELETE | `/admin/circles/:id`           | 删除社团 | "社团已删除"                |
| Events    | POST   | `/admin/events`                | 新建展会 | "展会创建成功"              |
| Events    | PUT    | `/admin/events/:id`            | 编辑展会 | "展会已保存"                |
| Events    | DELETE | `/admin/events/:id`            | 删除展会 | "展会已删除"                |
| Users     | POST   | `/admin/users`                 | 创建用户 | "用户创建成功"              |
| Users     | PUT    | `/admin/users/:id`             | 修改用户 | "用户信息已更新"            |
| Users     | DELETE | `/admin/users/:id`             | 删除用户 | "用户已删除"                |
| Bookmarks | POST   | `/circles/{circleId}/bookmark` | 切换收藏 | "已加入收藏" / "已取消收藏" |
| Auth      | POST   | `/auth/login`                  | 登录成功 | "登录成功"                  |
| Auth      | POST   | `/auth/logout`                 | 登出成功 | "已退出登录"                |

> 如果新增写操作接口，请更新此表以保持前后端文案一致。

## 3. 接口清单

| 模块 | 方法 | 路径                           | 查询/体参数                                   | 返回值 (`data`)                         | 封装函数            | React Hook          |
| ---- | ---- | ------------------------------ | --------------------------------------------- | --------------------------------------- | ------------------- | ------------------- |
| 展会 | GET  | `/events/{id}`                 | ‑                                             | `Event`                                 | `fetchGetEventsId`  | `useGetEventsId`    |
| 社团 | GET  | `/events/{id}/circles`         | ‑                                             | `CircleAppearance[]`                    | `fetchGetEventsIdCircles` | `useGetEventsIdCircles`   |
| 社团 | POST | `/circles/{circleId}/bookmark` | ‑                                             | `{ isBookmarked: boolean }`             | `toggleBookmark`    | `useToggleBookmark` |

### 3.1 `/events/{id}`

- **描述**：获取展会（Event）详情。
- **示例**：

```ts
const { data: event } = useGetEventsId({ pathParams: { id: 'reitaisai-16' } });
```

### 3.2 `/events/{id}/appearances`

- **描述**：查询指定展会下社团（Circle）列表。
- **分页**：`page` 从 1 开始，`pageSize` 默认 20。
- **分类筛选**：`category` 可传字符串数组或已逗号连接的字符串。
- **示例**：

```ts
const { data: circles } = useGetEventsIdCircles({ pathParams: { id }, /* queryParams 可根据后端支持补充 */ });
```

### 3.3 `/circles/{circleId}/bookmark`

- **描述**：切换社团收藏状态，后端会返回最新状态。
- **权限**：⚠️ 仅登录用户可调用，需在 Cookie 中携带 `auth_session`；未登录时返回 **401 Unauthorized**（业务错误码 `20001`）。
- **响应码**：
  - 200 OK — 收藏状态已切换，返回 `{ isBookmarked: boolean }`
  - 401 Unauthorized — 未登录 / Cookie 失效
  - 404 Not Found — `circleId` 不存在
- **乐观更新**：`useToggleBookmark` 已内置乐观更新逻辑。

```ts
const { mutate: toggle } = useToggleBookmark();

toggle('circle-123');
```

### 3.4 `/appearances`

- **描述**：通用参展记录检索接口，支持同时按 `circle_id`、`event_id`、`page`、`pageSize` 等参数筛选。
- **重要变更**：若在查询中携带 `event_id`，后端将在响应 Header 中加入 `Deprecation: true`，提示改用推荐的 `/events/{id}/appearances` 路由。该兼容调用将在 4 周后移除。

```ts
// 示例：按 circle_id 检索
const res = await http('/appearances', {
  params: { circle_id: 'circle-123', page: 1 },
});

// 过渡期示例：按 event_id 检索（收到 Deprecation Header）
const res = await http('/appearances', {
  params: { event_id: 'reitaisai-16', page: 1 },
});
```

返回数据结构：`{ items: AppearanceDto[]; total: number; page: number; pageSize: number }`

`AppearanceDto` 字段：

| 字段      | 类型           | 说明           |
| --------- | -------------- | -------------- |
| id        | string         | 参展记录 ID    |
| circle_id | string         | 社团 ID        |
| event_id  | string         | 展会 ID        |
| artist_id | string \| null | 作者 ID        |
| booth_id  | string         | 摊位号         |
| path      | string \| null | 展馆平面图路径 |

### 3.5 `/circles/{id}/appearances`

- **描述**：获取指定社团的参展历史，按展会时间倒序。
- **分页**：同样使用 `page`、`pageSize` 参数，默认 20 条。

```ts
const { data: res } = await http('/circles/circle-123/appearances', {
  params: { page: 1 },
});
```

返回示例：

```jsonc
{
  "items": [
    {
      "event_id": "reitaisai-22",
      "event_name": "博丽神社例大祭22",
      "event_date": "2025-05-15",
      "booth_id": "A12b",
    },
  ],
  "total": 8,
  "page": 1,
  "pageSize": 20,
}
```

## 4. React Query 使用约定

1. **统一 key**：`['event', id]`、`['circles', id, params]` 等，参数对象直接作为 key
   （v5 已支持深比较）。
2. **缓存时间**：查询默认 `staleTime = 5 min`，可在 Hook 内按需修改。
3. **错误处理**：全局处理放在 `src/components/react-query-provider.tsx`。
4. **占位数据**：列表接口采用 `placeholderData` 保留上一页，避免闪烁。

## 5. 未来规划

- 引入统一的 `code/data/message` 响应包裹。
- 使用 [Zod](https://zod.dev/) 对接口数据做运行时校验。
- 补充 E2E 与集成测试，确保接口契约。

---

最后更新：`2025-06-27` 