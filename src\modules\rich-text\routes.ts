import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import {
  richTextContentSchema,
  contentResponse,
  createContentRequest,
  batchContentRequest,
  EntityTypeEnum,
  ContentTypeEnum,
} from './schema';
import * as contentController from './controller';
import { registerOpenApiRoute } from '@/utils/openapiHelper';
import type { HonoApp } from '@/types';

const richTextRoutes = new OpenAPIHono<HonoApp>();

// 获取实体的所有内容
const getEntityContentRoute = createRoute({
  method: 'get',
  path: '/{entityType}/{entityId}/content',
  summary: '获取实体的所有富文本内容',
  description: '获取指定实体（事件、场馆、社团）的所有富文本内容',
  tags: ['Rich Text'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({
        example: 'event',
      }),
      entityId: z.string().openapi({
        example: 'reitaisai-22',
      }),
    }),
  },
  responses: {
    200: {
      description: '获取成功',
      content: {
        'application/json': {
          schema: contentResponse,
        },
      },
    },
    400: {
      description: '请求参数错误',
    },
    404: {
      description: '实体不存在',
    },
  },
});

// 获取实体的特定类型内容
const getEntityContentByTypeRoute = createRoute({
  method: 'get',
  path: '/{entityType}/{entityId}/content/{contentType}',
  summary: '获取实体的特定类型内容',
  description: '获取指定实体的特定类型富文本内容',
  tags: ['Rich Text'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({
        example: 'event',
      }),
      entityId: z.string().openapi({
        example: 'reitaisai-22',
      }),
      contentType: ContentTypeEnum.openapi({
        example: 'introduction',
      }),
    }),
  },
  responses: {
    200: {
      description: '获取成功',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              content: { type: 'string' },
            },
          },
        },
      },
    },
    400: {
      description: '请求参数错误',
    },
    404: {
      description: '内容不存在',
    },
  },
});

// 创建或更新单个内容
const createOrUpdateContentRoute = createRoute({
  method: 'post',
  path: '/{entityType}/{entityId}/content',
  summary: '创建或更新单个内容',
  description: '创建或更新指定实体的单个富文本内容',
  tags: ['Rich Text'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({
        example: 'event',
      }),
      entityId: z.string().openapi({
        example: 'reitaisai-22',
      }),
    }),
    body: {
      content: {
        'application/json': {
          schema: createContentRequest,
        },
      },
    },
  },
  responses: {
    201: {
      description: '创建成功',
      content: {
        'application/json': {
          schema: richTextContentSchema,
        },
      },
    },
    400: {
      description: '请求参数错误',
    },
  },
});

// 批量更新内容
const batchUpdateContentRoute = createRoute({
  method: 'put',
  path: '/{entityType}/{entityId}/content',
  summary: '批量更新内容',
  description: '批量更新指定实体的所有富文本内容',
  tags: ['Rich Text'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({
        example: 'event',
      }),
      entityId: z.string().openapi({
        example: 'reitaisai-22',
      }),
    }),
    body: {
      content: {
        'application/json': {
          schema: batchContentRequest,
        },
      },
    },
  },
  responses: {
    200: {
      description: '更新成功',
      content: {
        'application/json': {
          schema: contentResponse,
        },
      },
    },
    400: {
      description: '请求参数错误',
    },
  },
});

// 删除实体的所有内容
const deleteEntityContentRoute = createRoute({
  method: 'delete',
  path: '/{entityType}/{entityId}/content',
  summary: '删除实体的所有内容',
  description: '删除指定实体的所有富文本内容',
  tags: ['Rich Text'],
  request: {
    params: z.object({
      entityType: EntityTypeEnum.openapi({
        example: 'event',
      }),
      entityId: z.string().openapi({
        example: 'reitaisai-22',
      }),
    }),
  },
  responses: {
    200: {
      description: '删除成功',
    },
    400: {
      description: '请求参数错误',
    },
  },
});

// 图片上传路由（用于富文本编辑器）
const uploadImageRoute = createRoute({
  method: 'post',
  path: '/api/upload/images',
  summary: '富文本编辑器图片上传',
  description: '为富文本编辑器提供图片上传功能',
  tags: ['Rich Text'],
  request: {
    body: {
      content: {
        'multipart/form-data': {
          schema: {
            type: 'object',
            properties: {
              image: {
                type: 'string',
                format: 'binary',
                description: '图片文件',
              },
            },
            required: ['image'],
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: '上传成功',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              url: {
                type: 'string',
                example: '/images/content/1640995200000_example.jpg',
                description: '图片访问URL',
              },
            },
          },
        },
      },
    },
    400: {
      description: '请求参数错误或文件验证失败',
    },
    500: {
      description: '上传失败',
    },
  },
});

// 注册路由
registerOpenApiRoute(
  richTextRoutes,
  getEntityContentRoute,
  contentController.getEntityContent
);
registerOpenApiRoute(
  richTextRoutes,
  getEntityContentByTypeRoute,
  contentController.getEntityContentByType
);
registerOpenApiRoute(
  richTextRoutes,
  createOrUpdateContentRoute,
  contentController.createOrUpdateContent
);
registerOpenApiRoute(
  richTextRoutes,
  batchUpdateContentRoute,
  contentController.batchUpdateContent
);
registerOpenApiRoute(
  richTextRoutes,
  deleteEntityContentRoute,
  contentController.deleteEntityContent
);
registerOpenApiRoute(
  richTextRoutes,
  uploadImageRoute,
  contentController.uploadImage
);

export default richTextRoutes;
