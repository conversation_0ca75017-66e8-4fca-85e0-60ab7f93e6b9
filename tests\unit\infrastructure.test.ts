import type { Context } from 'hono';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import { <PERSON><PERSON><PERSON>, D<PERSON><PERSON><PERSON>ger, ConsoleLogger } from '@/infrastructure';
import * as circleService from '@/modules/circle/service';
import * as userService from '@/modules/user/service';

// ---------------------------
// MemoryCache 基本行为
// ---------------------------
describe('MemoryCache', () => {
  it('should set/get/delete values correctly', async () => {
    const cache = new MemoryCache();
    await cache.set('foo', { bar: 1 }, 10);
    const val = await cache.get<{ bar: number }>('foo');
    expect(val).toEqual({ bar: 1 });

    await cache.delete('foo');
    const deleted = await cache.get('foo');
    expect(deleted).toBeNull();
  });
});

// ---------------------------
// Service 缓存命中逻辑 (listCircles / listUsers)
// ---------------------------

function createMockDB(data: any[]): any {
  const prepareFn = vi.fn().mockImplementation((sql: string) => {
    return {
      all: async () => ({ results: data }),
      first: async () => {
        // 如果是 COUNT 查询，返回 total
        if (sql.includes('COUNT')) {
          return { total: data.length };
        }
        // 否则返回第一个记录
        return data[0] || null;
      },
      bind: (..._args: any[]) => ({
        all: async () => ({ results: data }),
        first: async () => {
          // 如果是 COUNT 查询，返回 total
          if (sql.includes('COUNT')) {
            return { total: data.length };
          }
          // 否则返回第一个记录
          return data[0] || null;
        },
      }),
    };
  });
  return { prepare: prepareFn };
}

describe('Service cache integration', () => {
  let cache: MemoryCache;
  let loggerCalls: string[];
  const logger: ConsoleLogger = {
    ...new ConsoleLogger(),
    debug: (msg: string) => {
      loggerCalls.push(msg);
    },
  } as ConsoleLogger;

  beforeEach(() => {
    cache = new MemoryCache();
    loggerCalls = [];
  });

  it('circleService.listCircles should hit cache on second call', async () => {
    const circles: any[] = [
      { id: 'c1', name: 'Circle1', urls: null, category: null } as any,
    ];
    const mockDB = createMockDB(circles);

    // first call -> miss
    const first = await circleService.listCircles(
      mockDB as any,
      'en',
      {},
      cache,
      logger
    );
    expect(first.items).toEqual(circles);
    expect(first.total).toBe(1);

    // second call -> should hit cache, thus prepare not called again
    const second = await circleService.listCircles(
      mockDB as any,
      'en',
      {},
      cache,
      logger
    );
    expect(second.items).toEqual(circles);
    expect(second.total).toBe(1);
    // 第一次调用执行 2 次 prepare (count + data)，第二次调用命中缓存不执行 prepare
    expect((mockDB.prepare as any).mock.calls.length).toBe(2);
    expect(loggerCalls.some((m) => m.includes('hit cache'))).toBe(true);
  });

  it('userService.listUsers should hit cache on second call', async () => {
    const users: any[] = [
      { id: 'u1', username: 'test', role: 'viewer' } as any,
    ];
    const mockDB = createMockDB(users);

    const first = await userService.listUsers(mockDB as any, cache, logger);
    expect(first).toEqual(users);

    const second = await userService.listUsers(mockDB as any, cache, logger);
    expect(second).toEqual(users);
    expect((mockDB.prepare as any).mock.calls.length).toBe(1);
  });
});

// ---------------------------
// D1Logger 写入日志行为
// ---------------------------

describe('D1Logger', () => {
  it('should write log into DB', async () => {
    // mock DB
    const runArgs: any[] = [];
    const mockDB = {
      prepare: vi.fn().mockImplementation((_sql: string) => {
        return {
          bind: (...args: any[]) => {
            return {
              run: async () => {
                runArgs.push(args);
              },
            };
          },
        };
      }),
    };

    // stub context
    const mockContext = {
      get: (key: string) => {
        if (key === 'auth') return { user: { id: 'u1', username: 'tester' } };
        return undefined;
      },
      env: { DB: mockDB },
    } as unknown as Context;

    const logger = new D1Logger(mockContext);
    await logger.info('CREATE_CIRCLE', {
      targetType: 'circle',
      targetId: 'c1',
    });

    expect(mockDB.prepare).toHaveBeenCalled();
    expect(runArgs.length).toBe(1);
    const boundParams = runArgs[0];
    expect(boundParams[3]).toBe('CREATE_CIRCLE'); // action param
    expect(boundParams[4]).toBe('circle');
    expect(boundParams[5]).toBe('c1');
  });
});
