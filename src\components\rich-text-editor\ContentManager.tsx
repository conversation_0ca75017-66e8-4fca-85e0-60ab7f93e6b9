'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Save, RotateCcw, AlertCircle, FileText, Star, MapPin, Bell } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { RichTextEditor } from './RichTextEditor';
import { useRichTextContent, type EntityType } from '@/hooks/useRichTextContent';
import { cn } from '@/lib/utils';

export interface ContentManagerProps {
  entityType: EntityType;
  entityId: string;
  className?: string;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export function ContentManager({
  entityType,
  entityId,
  className,
  autoSave = false,
  autoSaveDelay = 2000,
}: ContentManagerProps) {
  const [activeTab, setActiveTab] = useState('introduction');
  const t = useTranslations('richTextEditor');

  const CONTENT_TYPES = [
    {
      key: 'introduction' as const,
      label: t('contentTypes.introduction'),
      placeholder: t('placeholders.introduction'),
      icon: FileText,
    },
    {
      key: 'highlights' as const,
      label: t('contentTypes.highlights'),
      placeholder: t('placeholders.highlights'),
      icon: Star,
    },
    {
      key: 'guide' as const,
      label: t('contentTypes.guide'),
      placeholder: t('placeholders.guide'),
      icon: MapPin,
    },
    {
      key: 'notices' as const,
      label: t('contentTypes.notices'),
      placeholder: t('placeholders.notices'),
      icon: Bell,
    },
  ];

  const {
    content,
    updateContentType,
    saveAll,
    saveSingle,
    reset,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    error,
  } = useRichTextContent({
    entityType,
    entityId,
    autoSave,
    autoSaveDelay,
  });

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-12 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {t('errors.loadFailed')}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('title')}</h2>
          <p className="text-gray-600">
            {t('description')}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
            <Badge variant="secondary" className="animate-pulse">
              {t('status.unsavedChanges')}
            </Badge>
          )}

          {isSaving && (
            <Badge variant="outline">
              {t('status.saving')}
            </Badge>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={reset}
            disabled={!hasUnsavedChanges || isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            {t('buttons.reset')}
          </Button>

          <Button
            onClick={saveAll}
            disabled={!hasUnsavedChanges || isSaving}
            size="sm"
          >
            <Save className="h-4 w-4 mr-1" />
            {t('buttons.saveAll')}
          </Button>
        </div>
      </div>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          {CONTENT_TYPES.map((type) => (
            <TabsTrigger
              key={type.key}
              value={type.key}
              className="relative flex items-center gap-2"
            >
              <type.icon className="h-4 w-4" />
              {type.label}
              {content[type.key] && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full" />
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {CONTENT_TYPES.map((type) => (
          <TabsContent key={type.key} value={type.key} className="mt-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <type.icon className="h-5 w-5" />
                    <div>
                      <CardTitle>{type.label}</CardTitle>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => saveSingle(type.key)}
                    disabled={!content[type.key] || isSaving}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    {t('buttons.saveContent', { type: type.label })}
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <RichTextEditor
                  content={content[type.key] || ''}
                  onChange={(value) => updateContentType(type.key, value)}
                  placeholder={type.placeholder}
                  autoSave={autoSave}
                  autoSaveDelay={autoSaveDelay}
                  onSave={() => saveSingle(type.key, false)} // 自动保存不显示toast
                />
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Status */}
      {hasUnsavedChanges && !autoSave && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {t('status.unsavedWarning')}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
