import { OpenAPIHono, createRoute, z } from '@hono/zod-openapi';
import { zValidator } from '@hono/zod-validator';
import type { Context } from 'hono';
import { setCookie } from 'hono/cookie';

import { userSchema } from '../user/schema';
import { authSchema } from './schema';
import {
  SESSION_COOKIE_NAME,
  createUser,
  createSession,
  invalidateSession,
  verifyCredentials,
} from './service';
import { roleGuard } from '@/middlewares/roleGuard';
import { errorResponse } from '@/utils/schemas';
import { HonoApp } from '@/types';
import { jsonError } from '@/utils/errorResponse';
import { registerOpenApiRoute } from '@/utils/openapiHelper';

/**
 * Auth 路由（对应 /auth 前缀）
 */
export const routes = new OpenAPIHono<HonoApp>();

// ---------- OpenAPI Schema ----------
const registerRoute = createRoute({
  method: 'post',
  path: '/register',
  summary: 'Register a new user',
  tags: ['Auth'],
  request: {
    body: {
      content: {
        'application/json': {
          schema: authSchema,
        },
      },
    },
  },
  responses: {
    201: {
      description: 'User created',
      content: {
        'application/json': {
          schema: z.object({
            id: z.string().openapi({ example: 'uuid-123' }),
            username: z.string().openapi({ example: 'alice' }),
          }),
        },
      },
    },
    400: {
      description: 'Bad Request',
      content: { 'application/json': { schema: errorResponse } },
    },
    422: {
      description: 'Validation Error',
      content: { 'application/json': { schema: errorResponse } },
    },
  },
});

registerOpenApiRoute(routes, registerRoute, async (c: Context<HonoApp>) => {
  const { username, password } = (c.req as any).valid('json') as z.infer<
    typeof authSchema
  >;
  const db = c.env.DB;

  try {
    const user = await createUser(db, username, password);
    const sessionId = await createSession(db, user.id);

    setCookie(c, SESSION_COOKIE_NAME, sessionId, {
      path: '/',
      httpOnly: true,
      secure: c.env.ENVIRONMENT === 'production',
      sameSite: 'Lax',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });

    return c.json({ id: user.id, username: user.username }, 201);
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error';
    return jsonError(c, 40001, message, 400);
  }
});

/**
 * Route: POST /auth/login
 * Logs in a user and sets a session cookie.
 */
routes.post('/login', zValidator('json', authSchema), async (c) => {
  const { username, password } = c.req.valid('json');

  try {
    const user = await verifyCredentials(c.env.DB, username, password);

    const db = c.env.DB;
    const sessionId = await createSession(db, user.id);
    setCookie(c, SESSION_COOKIE_NAME, sessionId, {
      path: '/',
      httpOnly: true,
      secure: c.env.ENVIRONMENT === 'production',
      sameSite: 'Lax',
      maxAge: 60 * 60 * 24 * 30, // 30 days
    });

    return c.json({
      code: 0,
      message: '登录成功',
      data: {
        id: user.id,
        username: user.username,
        role: user.role,
      },
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      const msg = error.message;
      if (/USER_NOT_FOUND/i.test(msg)) {
        return jsonError(c, 40002, 'User not found', 400);
      }
      if (/PASSWORD_MISMATCH/i.test(msg)) {
        return jsonError(c, 40003, 'Password verification failed', 400);
      }
      return jsonError(c, 40002, msg, 400);
    }
    return jsonError(c, 40004, 'Unknown error', 400);
  }
});

// ---------- OpenAPI Routes ----------
const logoutRoute = createRoute({
  method: 'post',
  path: '/logout',
  summary: '退出登录',
  tags: ['Auth'],
  responses: {
    200: {
      description: '退出成功',
      content: {
        'application/json': {
          schema: z.object({
            message: z.string().openapi({
              example: '已退出登录',
            }),
          }),
        },
      },
    },
  },
});

const meRoute = createRoute({
  method: 'get',
  path: '/me',
  summary: '获取当前用户信息',
  tags: ['Auth'],
  responses: {
    200: {
      description: '用户信息',
      content: { 'application/json': { schema: userSchema } },
    },
    401: { description: 'Unauthorized' },
  },
});

const refreshRoute = createRoute({
  method: 'post',
  path: '/refresh',
  summary: '刷新会话',
  tags: ['Auth'],
  responses: {
    200: {
      description: '会话刷新成功',
      content: {
        'application/json': {
          schema: z.object({
            code: z.number().openapi({ example: 0 }),
            message: z.string().openapi({ example: '会话刷新成功' }),
            data: userSchema,
          }),
        },
      },
    },
    401: { description: 'Unauthorized' },
  },
});

// ---------- Register ----------
routes.use('/logout', roleGuard());
routes.use('/me', roleGuard());
routes.use('/refresh', roleGuard());

routes.openapi(logoutRoute, async (c) => {
  const authContext = c.get('auth');
  if (authContext.session) {
    await invalidateSession(c.env.DB, authContext.session.id);
  }

  setCookie(c, SESSION_COOKIE_NAME, '', { path: '/', maxAge: 0 });
  return c.json({ message: '已退出登录' });
});

routes.openapi(meRoute, (c) => {
  const user = c.get('auth').user;
  return c.json(user);
});

routes.openapi(refreshRoute, async (c) => {
  const authContext = c.get('auth');
  const user = authContext.user;

  if (!user || !authContext.session) {
    return jsonError(c, 20001, '未登录', 401);
  }

  // 延长会话过期时间（可选）
  const db = c.env.DB;
  const sessionExpiresIn = 1000 * 60 * 60 * 24 * 30; // 30 天
  const newExpiresAt = new Date(Date.now() + sessionExpiresIn);

  try {
    await db
      .prepare('UPDATE auth_session SET expires_at = ? WHERE id = ?')
      .bind(newExpiresAt.toISOString(), authContext.session.id)
      .run();
  } catch (error) {
    // 如果更新失败，仍然返回用户信息，因为会话仍然有效
    console.warn('Failed to extend session:', error);
  }

  return c.json({
    code: 0,
    message: '会话刷新成功',
    data: user,
  });
});
