"use client";

import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import Link from "next/link";
import { UserCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/user";
import { RadixButton } from "@/components/ui/radix-components";

// 菜单项组件
interface MenuItemProps {
  href?: string;
  onClick?: () => void;
  children: React.ReactNode;
}

function MenuItem({ href, onClick, children }: MenuItemProps) {
  const className = "flex items-center px-3 py-2 text-sm rounded-lg hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 outline-none focus:bg-gradient-to-r focus:from-blue-50 focus:to-purple-50 dark:focus:from-blue-950 dark:focus:to-purple-950 cursor-pointer w-full transition-all duration-300";

  if (href) {
    return (
      <DropdownMenu.Item asChild>
        <Link href={href} className={className}>
          {children}
        </Link>
      </DropdownMenu.Item>
    );
  }

  return (
    <DropdownMenu.Item onSelect={onClick} className={className}>
      {children}
    </DropdownMenu.Item>
  );
}

// 用户信息显示组件
interface UserInfoProps {
  user: { username: string; email?: string };
}

function UserInfo({ user }: UserInfoProps) {
  return (
    <div className="flex items-center gap-2 p-2">
      <UserCircle className="h-8 w-8" />
      <div className="flex flex-col">
        <p className="text-sm font-medium">{user.username}</p>
        {user.email && (
          <p className="text-xs text-muted-foreground">{user.email}</p>
        )}
      </div>
    </div>
  );
}

// 分隔线组件
function MenuSeparator() {
  return <DropdownMenu.Separator className="h-px bg-slate-200/50 dark:bg-slate-700/50 my-2" />;
}

// 未登录用户菜单
function GuestMenu() {
  return (
    <>
      <MenuItem href="/login">登录</MenuItem>
      <MenuItem href="/register">注册</MenuItem>
    </>
  );
}

// 已登录用户菜单
interface AuthenticatedMenuProps {
  user: { username: string; email?: string };
  onLogout: () => void;
}

function AuthenticatedMenu({ user, onLogout }: AuthenticatedMenuProps) {
  return (
    <>
      <UserInfo user={user} />
      <MenuSeparator />
      <MenuItem href="/profile">用户中心</MenuItem>
      <MenuItem href="/admin">后台管理</MenuItem>
      <MenuSeparator />
      <MenuItem onClick={onLogout}>退出登录</MenuItem>
    </>
  );
}

// 用户菜单触发按钮
interface UserMenuTriggerProps {
  isLoading?: boolean;
}

function UserMenuTrigger({ isLoading }: UserMenuTriggerProps) {
  return (
    <DropdownMenu.Trigger asChild>
      <RadixButton
        variant="ghost"
        size="sm"
        className={cn(
          "w-10 h-10 px-0 transition-all text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 rounded-xl backdrop-blur-sm",
          isLoading && "opacity-50"
        )}
        disabled={isLoading}
        aria-label="用户菜单"
      >
        <UserCircle className="h-5 w-5 transition-transform hover:scale-110" />
      </RadixButton>
    </DropdownMenu.Trigger>
  );
}

export default function UserMenu() {
  const { user, isLoading, logout } = useAuth();

  // 在加载状态显示不可点击的图标
  if (isLoading) {
    return (
      <RadixButton
        variant="ghost"
        size="sm"
        className="w-10 h-10 px-0 opacity-50 text-slate-700 dark:text-slate-300 rounded-xl"
        disabled
        aria-label="用户菜单加载中"
      >
        <UserCircle className="h-5 w-5" />
      </RadixButton>
    );
  }

  return (
    <DropdownMenu.Root>
      <UserMenuTrigger />
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="end"
          className="min-w-[224px] bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 rounded-xl p-2 shadow-xl shadow-slate-200/20 dark:shadow-slate-900/20 z-50"
          sideOffset={5}
        >
          {!user ? (
            <GuestMenu />
          ) : (
            <AuthenticatedMenu user={user} onLogout={logout} />
          )}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}