'use client';

import React from 'react';
import { Heart, TrendingUp, Calendar, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useBookmarkStats } from '@/hooks/useBookmark';

interface BookmarkStatsProps {
  className?: string;
  showTitle?: boolean;
  variant?: 'default' | 'compact';
}

/**
 * BookmarkStats 收藏统计组件
 * 
 * 功能特性：
 * - 显示总收藏数
 * - 显示最近收藏数（7天、30天）
 * - 显示收藏趋势
 * - 响应式设计
 * - 紧凑模式支持
 */
export function BookmarkStats({
  className,
  showTitle = true,
  variant = 'default',
}: BookmarkStatsProps) {
  const { data: statsData, isLoading, error } = useBookmarkStats();

  if (error) {
    return (
      <Card className={cn('border-destructive/20', className)}>
        <CardContent className="p-6 text-center">
          <p className="text-sm text-muted-foreground">
            Failed to load bookmark statistics
          </p>
        </CardContent>
      </Card>
    );
  }

  const stats = statsData?.data;

  if (variant === 'compact') {
    return (
      <CompactBookmarkStats 
        stats={stats} 
        isLoading={isLoading} 
        className={className} 
      />
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {showTitle && (
        <div>
          <h2 className="text-xl font-semibold mb-2">Bookmark Statistics</h2>
          <p className="text-muted-foreground">
            Overview of your bookmarked circles
          </p>
        </div>
      )}

      {isLoading ? (
        <BookmarkStatsSkeleton />
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* 总收藏数 */}
          <StatCard
            title="Total Bookmarks"
            value={stats?.totalBookmarks || 0}
            icon={Heart}
            description="All time"
            trend={stats?.totalBookmarks > 0 ? 'positive' : 'neutral'}
          />

          {/* 本周新增 */}
          <StatCard
            title="This Week"
            value={stats?.weeklyBookmarks || 0}
            icon={TrendingUp}
            description="Last 7 days"
            trend={getWeeklyTrend(stats?.weeklyBookmarks, stats?.previousWeeklyBookmarks)}
          />

          {/* 本月新增 */}
          <StatCard
            title="This Month"
            value={stats?.monthlyBookmarks || 0}
            icon={Calendar}
            description="Last 30 days"
            trend={getMonthlyTrend(stats?.monthlyBookmarks, stats?.previousMonthlyBookmarks)}
          />

          {/* 平均每周 */}
          <StatCard
            title="Weekly Average"
            value={calculateWeeklyAverage(stats?.totalBookmarks, stats?.firstBookmarkDate)}
            icon={BarChart3}
            description="Per week"
            trend="neutral"
            isDecimal
          />
        </div>
      )}

      {/* 详细统计 */}
      {!isLoading && stats && (
        <DetailedStats stats={stats} />
      )}
    </div>
  );
}

/**
 * 紧凑模式统计组件
 */
function CompactBookmarkStats({
  stats,
  isLoading,
  className,
}: {
  stats: any;
  isLoading: boolean;
  className?: string;
}) {
  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="w-10 h-10 bg-muted rounded-lg animate-pulse" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded w-24 animate-pulse" />
              <div className="h-3 bg-muted rounded w-16 animate-pulse" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
            <Heart className="w-5 h-5 text-primary" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">
                {stats?.totalBookmarks || 0}
              </span>
              {stats?.weeklyBookmarks > 0 && (
                <Badge variant="secondary" className="text-xs">
                  +{stats.weeklyBookmarks} this week
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground">
              Bookmarked Circles
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 单个统计卡片
 */
function StatCard({
  title,
  value,
  icon: Icon,
  description,
  trend,
  isDecimal = false,
}: {
  title: string;
  value: number;
  icon: React.ElementType;
  description: string;
  trend: 'positive' | 'negative' | 'neutral';
  isDecimal?: boolean;
}) {
  const trendColors = {
    positive: 'text-green-600',
    negative: 'text-red-600',
    neutral: 'text-muted-foreground',
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {isDecimal ? value.toFixed(1) : value.toLocaleString()}
        </div>
        <p className={cn('text-xs', trendColors[trend])}>
          {description}
        </p>
      </CardContent>
    </Card>
  );
}

/**
 * 详细统计信息
 */
function DetailedStats({ stats }: { stats: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Detailed Statistics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h4 className="font-medium mb-2">Recent Activity</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last 7 days:</span>
                <span className="font-medium">{stats.weeklyBookmarks || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last 30 days:</span>
                <span className="font-medium">{stats.monthlyBookmarks || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Last 90 days:</span>
                <span className="font-medium">{stats.quarterlyBookmarks || 0}</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Timeline</h4>
            <div className="space-y-2 text-sm">
              {stats.firstBookmarkDate && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">First bookmark:</span>
                  <span className="font-medium">
                    {new Date(stats.firstBookmarkDate).toLocaleDateString()}
                  </span>
                </div>
              )}
              {stats.lastBookmarkDate && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Latest bookmark:</span>
                  <span className="font-medium">
                    {new Date(stats.lastBookmarkDate).toLocaleDateString()}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-muted-foreground">Active days:</span>
                <span className="font-medium">{stats.activeDays || 0}</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 骨架屏组件
 */
function BookmarkStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 bg-muted rounded w-20 animate-pulse" />
            <div className="h-4 w-4 bg-muted rounded animate-pulse" />
          </CardHeader>
          <CardContent>
            <div className="h-8 bg-muted rounded w-16 mb-2 animate-pulse" />
            <div className="h-3 bg-muted rounded w-12 animate-pulse" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * 工具函数
 */
function getWeeklyTrend(current?: number, previous?: number): 'positive' | 'negative' | 'neutral' {
  if (!current || !previous) return 'neutral';
  if (current > previous) return 'positive';
  if (current < previous) return 'negative';
  return 'neutral';
}

function getMonthlyTrend(current?: number, previous?: number): 'positive' | 'negative' | 'neutral' {
  if (!current || !previous) return 'neutral';
  if (current > previous) return 'positive';
  if (current < previous) return 'negative';
  return 'neutral';
}

function calculateWeeklyAverage(total?: number, firstDate?: string): number {
  if (!total || !firstDate) return 0;
  
  const start = new Date(firstDate);
  const now = new Date();
  const weeks = Math.max(1, Math.ceil((now.getTime() - start.getTime()) / (7 * 24 * 60 * 60 * 1000)));
  
  return total / weeks;
}

export default BookmarkStats;
