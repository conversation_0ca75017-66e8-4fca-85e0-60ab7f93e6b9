'use client';

import React from 'react';
import { motion } from 'motion/react';
import { Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface BookmarkHeaderProps {
  totalCount: number;
  isLoading?: boolean;
}

export function BookmarkHeader({ totalCount, isLoading = false }: BookmarkHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
            我的收藏
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            管理您收藏的社团
          </p>
        </div>
        <div className="flex items-center gap-2">
          {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
          <Badge variant="secondary" className="text-sm">
            {totalCount} 项收藏
          </Badge>
        </div>
      </div>
    </motion.div>
  );
}
