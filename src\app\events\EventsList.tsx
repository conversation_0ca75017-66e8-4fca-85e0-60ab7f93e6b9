'use client'

import Image from "next/image";
import Link from "next/link";
import { useEffect, useMemo, useState } from "react";

import { useGetEvents } from "@/api/generated/ayafeedComponents";
import * as Schemas from "@/api/generated/ayafeedSchemas";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import EventCardSkeleton from "@/components/events/EventCardSkeleton";
import EmptyState from "@/components/events/EmptyState";
import { EventImageDisplay } from "@/components/events/EventImageDisplay";
import { useDebounce } from "@/hooks";
import { getValidImageUrl } from "@/lib/image-utils";

const PAGE_SIZE = 12;

export default function EventsList() {
  // 搜索关键字
  const [keyword, setKeyword] = useState("");
  const debouncedKeyword = useDebounce(keyword, 300);

  // 分页
  const [page, setPage] = useState(1);

  // 当关键字变化时重置页数
  useEffect(() => {
    setPage(1);
  }, [debouncedKeyword]);

  // 组装查询变量
  const vars = useMemo(
    () =>
      ({
        queryParams: {
          page: String(page),
          pageSize: String(PAGE_SIZE),
          keyword: debouncedKeyword || undefined,
        },
      }) as const,
    [page, debouncedKeyword]
  );

  const { data, isLoading, isFetching } = useGetEvents(vars, {
    staleTime: 60 * 1000,
  });

  // events 与分页信息
  const events = (data?.items ?? []) as Schemas.PaginatedResult["items"];
  const total = data?.total ?? 0;
  const hasMore = page * PAGE_SIZE < total;

  return (
    <div className="space-y-8" data-testid="events-list">
      {/* 页面标题 */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
          展会列表
        </h1>
        <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
          发现精彩的同人展会活动，探索创意与文化的交汇点
        </p>
      </div>

      {/* 搜索框 */}
      <div className="flex justify-center">
        <Input
          placeholder="搜索展会…"
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
          className="max-w-md bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 transition-all duration-300"
        />
      </div>

      {/* 列表区域 */}
      <div data-testid="events-grid">
        {isLoading && page === 1 ? (
          <SkeletonGrid count={PAGE_SIZE} />
        ) : events.length === 0 ? (
          <EmptyState message="暂无展会信息" />
        ) : (
          <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
            {events.map((evt) => (
              <EventCard key={evt.id} evt={evt as any} />
            ))}
            {/* 下一页加载中骨架 */}
            {isFetching && page > 1 && <SkeletonGrid count={4} />}
          </div>
        )}
      </div>

      {/* 加载更多 */}
      {hasMore && (
        <div className="flex justify-center">
          <Button
            onClick={() => setPage((p) => p + 1)}
            disabled={isFetching}
            variant="outline"
          >
            {isFetching ? "加载中…" : "加载更多"}
          </Button>
        </div>
      )}
    </div>
  );
}

/** 事件卡片 - 现代艺术风格 */
function EventCard({ evt }: { evt: any }) {
  return (
    <Link
      href={`/events/${evt.id}`}
      className="group focus:outline-none"
      data-testid="event-card"
    >
      <Card className="overflow-hidden group-hover:shadow-2xl group-hover:shadow-blue-500/10 group-hover:-translate-y-2 group-hover:scale-105 transition-all duration-300 border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl">
        <CardHeader className="p-0 relative">
          {/* 优先使用新的图片 API，回退到传统 image_url */}
          {evt.id ? (
            <EventImageDisplay
              eventId={evt.id}
              imageType="poster"
              variant="medium"
              width={400}
              height={240}
              alt={evt.name}
              className="w-full h-48 group-hover:scale-110 transition-transform duration-300"
              showLoading={true}
              showError={false}
            />
          ) : (
            <Image
              src={getValidImageUrl(evt.image_url)}
              alt={evt.name}
              width={400}
              height={240}
              className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
            />
          )}
          {/* 悬停遮罩 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          {/* 装饰性渐变边框 */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ mixBlendMode: 'overlay' }} />
        </CardHeader>
        <CardContent className="p-4 space-y-2">
          <h3 className="font-semibold truncate bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
            {evt.name}
          </h3>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            {evt.date}
            {evt.venue_name ? ` ・ ${evt.venue_name}` : ""}
          </p>
        </CardContent>
      </Card>
    </Link>
  );
}

/** 骨架网格 */
function SkeletonGrid({ count }: { count: number }) {
  return (
    <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
      {Array.from({ length: count }).map((_, i) => (
        <EventCardSkeleton key={i} />
      ))}
    </div>
  );
} 