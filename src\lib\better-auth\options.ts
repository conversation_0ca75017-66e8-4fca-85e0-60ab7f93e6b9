import { BetterAuthOptions } from 'better-auth';
import { username } from 'better-auth/plugins';

/**
 * Better Auth 配置选项 - 适配 Cloudflare D1
 */
export const betterAuthOptions: BetterAuthOptions = {
  /**
   * 应用名称
   */
  appName: 'AyaFeed',

  /**
   * Better Auth API 基础路径
   */
  basePath: '/auth',

  /**
   * 启用邮箱密码登录
   */
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // D1 环境下先禁用邮件验证
    minPasswordLength: 6,
    maxPasswordLength: 128,
  },

  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30天
    updateAge: 60 * 60 * 24, // 1天更新一次
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5分钟
    },
  },

  /**
   * 用户配置 - 支持角色系统
   */
  user: {
    additionalFields: {
      role: {
        type: 'string',
        required: true,
        defaultValue: 'user', // 与现有系统保持一致
      },
    },
  },

  /**
   * 数据库钩子 - 确保新用户有默认角色
   */
  databaseHooks: {
    user: {
      create: {
        before: async (user) => {
          return {
            data: {
              ...user,
              role: (user as any).role || 'user', // 默认角色
            },
          };
        },
      },
    },
  },

  /**
   * 高级配置
   */
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
    defaultCookieAttributes: {
      sameSite: 'lax',
      httpOnly: true,
      secure: false, // 开发环境设为 false
      path: '/',
    },
    database: {
      generateId: () => crypto.randomUUID(),
    },
  },

  rateLimit: {
    window: 60,
    max: 100,
    storage: 'memory',
  },

  trustedOrigins: [
    'http://localhost:3000',
    'http://localhost:8787',
    'https://ayafeed.com',
  ],

  /**
   * 插件配置
   */
  plugins: [
    // 用户名登录插件 - 支持用户名和邮箱双重登录
    username({
      minUsernameLength: 3,
      maxUsernameLength: 30,
      usernameValidator: (username) => {
        // 只允许字母、数字、下划线和点
        const usernameRegex = /^[a-zA-Z0-9_.]+$/;
        return usernameRegex.test(username);
      },
    }),
  ],
};
