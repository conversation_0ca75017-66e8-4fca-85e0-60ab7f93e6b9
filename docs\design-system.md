# Ayafeed 设计系统文档

## 📋 目录
- [设计理念](#设计理念)
- [色彩系统](#色彩系统)
- [布局系统](#布局系统)
- [组件规范](#组件规范)
- [动画系统](#动画系统)
- [响应式设计](#响应式设计)
- [实施指南](#实施指南)

## 🎨 设计理念

### 核心价值
- **现代艺术感**: 融合渐变、毛玻璃效果和精美动画
- **一致性**: 统一的视觉语言和交互模式
- **可访问性**: 支持深色模式和无障碍访问
- **性能优先**: 优化动画性能和用户体验

### 设计原则
1. **简洁优雅**: 避免过度装饰，注重内容呈现
2. **层次清晰**: 通过视觉层次引导用户注意力
3. **交互友好**: 提供清晰的反馈和流畅的过渡
4. **品牌一致**: 保持 Ayafeed 品牌特色

## 🌈 色彩系统

### 主色调
```css
/* 渐变背景 */
--bg-gradient-light: linear-gradient(to bottom right, 
  rgb(248 250 252), rgb(255 255 255), rgb(241 245 249));
--bg-gradient-dark: linear-gradient(to bottom right, 
  rgb(2 6 23), rgb(15 23 42), rgb(30 41 59));

/* 品牌色 */
--brand-primary: linear-gradient(to right, rgb(59 130 246), rgb(147 51 234));
--brand-secondary: linear-gradient(to right, rgb(147 51 234), rgb(219 39 119));
--brand-accent: linear-gradient(to right, rgb(6 182 212), rgb(59 130 246));
```

### 文字色彩
```css
/* 渐变文字 */
--text-gradient-light: linear-gradient(to right, rgb(15 23 42), rgb(71 85 105));
--text-gradient-dark: linear-gradient(to right, rgb(241 245 249), rgb(148 163 184));

/* 悬停状态 */
--text-gradient-hover: linear-gradient(to right, rgb(37 99 235), rgb(147 51 234));
```

### 装饰色彩
```css
/* 背景装饰元素 */
--decoration-blue: rgba(59, 130, 246, 0.1);
--decoration-purple: rgba(147, 51, 234, 0.1);
--decoration-pink: rgba(219, 39, 119, 0.1);
--decoration-cyan: rgba(6, 182, 212, 0.05);
```

## 📐 布局系统

### 容器规范
```css
/* 主容器 */
.container-main {
  max-width: 1280px; /* max-w-7xl */
  margin: 0 auto;
  padding: 0 1rem; /* px-4 */
}

/* 响应式内边距 */
@media (min-width: 768px) {
  .container-main { padding: 0 3rem; } /* md:px-12 */
}
```

### 间距系统
```css
/* 标准间距 */
--spacing-xs: 0.5rem;   /* 8px */
--spacing-sm: 1rem;     /* 16px */
--spacing-md: 1.5rem;   /* 24px */
--spacing-lg: 2rem;     /* 32px */
--spacing-xl: 3rem;     /* 48px */
--spacing-2xl: 4rem;    /* 64px */
```

### 背景装饰模板
```css
/* 标准背景装饰 */
.bg-decorative {
  position: relative;
  overflow: hidden;
  background: var(--bg-gradient-light);
}

.dark .bg-decorative {
  background: var(--bg-gradient-dark);
}

.bg-decorative::before {
  content: '';
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
}

/* 装饰元素 */
.decoration-orb-1 {
  position: absolute;
  top: -10rem;
  right: -10rem;
  width: 20rem;
  height: 20rem;
  background: linear-gradient(to bottom right, var(--decoration-blue), var(--decoration-purple));
  border-radius: 50%;
  filter: blur(3rem);
}

.decoration-orb-2 {
  position: absolute;
  bottom: -10rem;
  left: -10rem;
  width: 20rem;
  height: 20rem;
  background: linear-gradient(to bottom right, var(--decoration-purple), var(--decoration-pink));
  border-radius: 50%;
  filter: blur(3rem);
}

.decoration-orb-3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24rem;
  height: 24rem;
  background: linear-gradient(to bottom right, var(--decoration-cyan), var(--decoration-blue));
  border-radius: 50%;
  filter: blur(3rem);
}
```

## 🧩 组件规范

### 卡片组件
```css
.card-modern {
  overflow: hidden;
  border: 1px solid rgba(148, 163, 184, 0.5); /* border-slate-200/50 */
  background: linear-gradient(to bottom right, 
    rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.8));
  backdrop-filter: blur(12px);
  border-radius: 0.75rem; /* rounded-xl */
  transition: all 0.3s ease;
}

.dark .card-modern {
  border-color: rgba(71, 85, 105, 0.5); /* dark:border-slate-700/50 */
  background: linear-gradient(to bottom right, 
    rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.8));
}

.card-modern:hover {
  transform: translateY(-0.5rem) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.1);
}
```

### 按钮组件
```css
.btn-modern {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(148, 163, 184, 0.5);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.dark .btn-modern {
  background: rgba(30, 41, 59, 0.6);
  border-color: rgba(71, 85, 105, 0.5);
}

.btn-modern:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(59, 130, 246, 0.3);
}

.dark .btn-modern:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(59, 130, 246, 0.6);
}
```

### 输入框组件
```css
.input-modern {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(148, 163, 184, 0.5);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.dark .input-modern {
  background: rgba(30, 41, 59, 0.6);
  border-color: rgba(71, 85, 105, 0.5);
}

.input-modern:focus {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.dark .input-modern:focus {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}
```

### 导航栏组件
```css
.navbar-modern {
  position: sticky;
  top: 0;
  z-index: 50;
  width: 100%;
  border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  background: linear-gradient(to right, 
    rgba(255, 255, 255, 0.95), 
    rgba(248, 250, 252, 0.95), 
    rgba(255, 255, 255, 0.95));
  backdrop-filter: blur(12px);
  box-shadow: 0 10px 15px -3px rgba(148, 163, 184, 0.2);
}

.dark .navbar-modern {
  border-bottom-color: rgba(71, 85, 105, 0.3);
  background: linear-gradient(to right, 
    rgba(15, 23, 42, 0.95), 
    rgba(30, 41, 59, 0.95), 
    rgba(15, 23, 42, 0.95));
  box-shadow: 0 10px 15px -3px rgba(15, 23, 42, 0.2);
}
```

## ⚡ 动画系统

### 基础动画
```css
/* 悬停提升效果 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-0.5rem);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 缩放效果 */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* 渐变过渡 */
.gradient-transition {
  transition: background-image 0.3s ease, color 0.3s ease;
}
```

### 入场动画
```css
/* 淡入上移 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 错位动画 */
.stagger-animation {
  animation-delay: calc(var(--stagger-delay, 0) * 0.1s);
}
```

### 性能优化
```css
/* GPU 加速 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* 减少重绘 */
.optimized-animation {
  contain: layout style paint;
}
```

## 📱 响应式设计

### 断点系统
```css
/* Tailwind CSS 断点 */
--breakpoint-sm: 640px;   /* 小屏幕 */
--breakpoint-md: 768px;   /* 中等屏幕 */
--breakpoint-lg: 1024px;  /* 大屏幕 */
--breakpoint-xl: 1280px;  /* 超大屏幕 */
--breakpoint-2xl: 1536px; /* 超超大屏幕 */
```

### 响应式布局模式
```css
/* 网格布局 */
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 响应式间距 */
.responsive-spacing {
  padding: 1rem;
}

@media (min-width: 768px) {
  .responsive-spacing {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-spacing {
    padding: 3rem;
  }
}
```

### 移动端优化
```css
/* 触摸友好的按钮 */
.touch-friendly {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
}

/* 移动端导航 */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(148, 163, 184, 0.2);
  padding: 0.5rem;
}

@media (min-width: 768px) {
  .mobile-nav {
    display: none;
  }
}
```

## 🛠️ 实施指南

### Tailwind CSS 类名映射

#### 背景和装饰
```html
<!-- 标准页面背景 -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden">
  <!-- 装饰元素 -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/5 to-blue-600/5 rounded-full blur-3xl"></div>
  </div>

  <!-- 页面内容 -->
  <div class="relative">
    <!-- 内容区域 -->
  </div>
</div>
```

#### 现代卡片
```html
<div class="overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10 hover:-translate-y-2 hover:scale-105 border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl rounded-xl">
  <!-- 卡片内容 -->
</div>
```

#### 渐变文字
```html
<h1 class="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
  标题文字
</h1>
```

#### 现代按钮
```html
<button class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:bg-white/80 dark:hover:bg-slate-800/80 transition-all duration-300 rounded-xl px-4 py-2">
  按钮文字
</button>
```

#### 现代输入框
```html
<input class="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-300 rounded-xl" />
```

### React/Next.js 组件示例

#### 页面布局组件
```tsx
import { motion } from 'motion/react'

interface ModernPageLayoutProps {
  children: React.ReactNode
  className?: string
}

export function ModernPageLayout({ children, className = '' }: ModernPageLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden ${className}`}>
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/5 to-blue-600/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative">
        {children}
      </div>
    </div>
  )
}
```

#### 现代卡片组件
```tsx
import { motion } from 'motion/react'

interface ModernCardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
}

export function ModernCard({ children, className = '', hover = true }: ModernCardProps) {
  return (
    <motion.div
      className={`overflow-hidden border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl rounded-xl transition-all duration-300 ${hover ? 'hover:shadow-2xl hover:shadow-blue-500/10' : ''} ${className}`}
      whileHover={hover ? { y: -8, scale: 1.02 } : undefined}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  )
}
```

#### 渐变标题组件
```tsx
interface GradientHeadingProps {
  children: React.ReactNode
  level?: 1 | 2 | 3 | 4 | 5 | 6
  className?: string
}

export function GradientHeading({ children, level = 1, className = '' }: GradientHeadingProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements
  const sizeClasses = {
    1: 'text-4xl',
    2: 'text-3xl',
    3: 'text-2xl',
    4: 'text-xl',
    5: 'text-lg',
    6: 'text-base'
  }

  return (
    <Tag className={`font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent ${sizeClasses[level]} ${className}`}>
      {children}
    </Tag>
  )
}
```

### 最佳实践

#### 性能优化
1. **使用 CSS 变换而非改变布局属性**
   ```css
   /* ✅ 好的做法 */
   transform: translateY(-0.5rem);

   /* ❌ 避免 */
   top: -0.5rem;
   ```

2. **合理使用 will-change**
   ```css
   .hover-element:hover {
     will-change: transform;
   }

   .hover-element {
     will-change: auto; /* 动画结束后重置 */
   }
   ```

3. **使用 GPU 加速**
   ```css
   .gpu-accelerated {
     transform: translateZ(0);
     backface-visibility: hidden;
   }
   ```

#### 可访问性
1. **保持足够的对比度**
2. **提供键盘导航支持**
3. **使用语义化 HTML**
4. **添加适当的 ARIA 标签**

#### 代码组织
1. **使用 CSS 自定义属性管理主题**
2. **创建可复用的组件类**
3. **保持命名一致性**
4. **文档化设计决策**

### 工具和资源

#### 推荐工具
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Framer Motion**: React 动画库
- **Radix UI**: 无障碍 UI 组件
- **Figma**: 设计协作工具

#### 检查清单
- [ ] 响应式设计测试
- [ ] 深色模式兼容性
- [ ] 动画性能检查
- [ ] 可访问性审核
- [ ] 跨浏览器兼容性
- [ ] 移动端体验优化

---

## 📝 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 建立核心设计系统
- 定义色彩和布局规范
- 创建组件库基础

---

*本文档将随着设计系统的演进持续更新。如有疑问或建议，请联系设计团队。*
```
