'use client';

import React, { useCallback, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import { Color } from '@tiptap/extension-color';
import { TextStyle } from '@tiptap/extension-text-style';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';

import { RichTextToolbar } from './RichTextToolbar';
import { cn } from '@/lib/utils';

export interface RichTextEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  onSave?: () => void;
  placeholder?: string;
  className?: string;
  editable?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export function RichTextEditor({
  content = '',
  onChange,
  onSave,
  placeholder = 'Start writing...',
  className,
  editable = true,
  autoSave = false,
  autoSaveDelay = 2000,
}: RichTextEditorProps) {
  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
        listItem: false,
        link: false, // 禁用默认Link扩展，使用自定义配置
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'rich-text-bullet-list',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'rich-text-ordered-list',
        },
      }),
      ListItem,
      Image.configure({
        HTMLAttributes: {
          class: 'rich-text-image',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'rich-text-link',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle,
      Color,
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
          'min-h-[200px] p-4 border border-gray-200 rounded-md',
          className
        ),
        'data-placeholder': placeholder,
      },
    },
  });

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !editor || !onSave) return;

    const timer = setTimeout(() => {
      onSave();
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [content, autoSave, autoSaveDelay, onSave, editor]);

  // Update editor content when prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  const handleSave = useCallback(() => {
    onSave?.();
  }, [onSave]);

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-12 bg-gray-200 rounded mb-4"></div>
        <div className="h-48 bg-gray-200 rounded"></div>
      </div>
    );
  }

  return (
    <div className="rich-text-editor">
      <RichTextToolbar 
        editor={editor} 
        onSave={onSave ? handleSave : undefined}
      />
      <EditorContent 
        editor={editor}
        className={cn(
          'rich-text-content',
          !editable && 'pointer-events-none opacity-75'
        )}
      />
    </div>
  );
}
