/**
 * 事件详情页面导航工具函数
 */

/**
 * 切换到会场信息标签页并滚动到地图模块
 */
export const scrollToVenueMap = () => {
  console.log('开始执行会场点击功能')

  // 尝试多种选择器来找到会场标签页
  const selectors = [
    'button[data-state][value="venue"]', // Radix UI Tabs.Trigger的实际DOM结构
    'button[role="tab"][value="venue"]',
    '[value="venue"]',
    '[data-value="venue"]'
  ]

  let venueTab: HTMLElement | null = null

  // 尝试不同的选择器
  for (const selector of selectors) {
    try {
      venueTab = document.querySelector(selector) as HTMLElement
      console.log(`尝试选择器 ${selector}:`, venueTab)
      if (venueTab) break
    } catch (e) {
      console.log(`选择器 ${selector} 失败:`, e)
    }
  }

  // 如果还是找不到，尝试通过文本内容查找
  if (!venueTab) {
    console.log('通过文本内容查找会场标签页')
    const allTabs = document.querySelectorAll('button[role="tab"]')
    console.log('找到的所有标签页:', allTabs)
    for (const tab of allTabs) {
      console.log('检查标签页文本:', tab.textContent)
      if (tab.textContent?.includes('会场信息')) {
        venueTab = tab as HTMLElement
        console.log('通过文本找到会场标签页:', venueTab)
        break
      }
    }
  }

  console.log('最终找到的会场标签页:', venueTab)

  if (venueTab) {
    console.log('点击会场标签页')
    venueTab.click()
    // 等待标签页切换完成后滚动到地图模块
    setTimeout(() => {
      const mapSection = document.getElementById('venue-map-section')
      console.log('找到的地图区域:', mapSection)
      if (mapSection) {
        // 计算滚动位置，留出顶部导航空间
        const offsetTop = mapSection.offsetTop - 120
        console.log('滚动到位置:', offsetTop)
        window.scrollTo({
          top: Math.max(0, offsetTop), // 确保不会滚动到负数位置
          behavior: 'smooth'
        })
      } else {
        console.warn('未找到地图区域 #venue-map-section')
      }
    }, 250) // 稍微增加延迟确保DOM更新完成
  } else {
    console.warn('未找到会场信息标签页')
    // 输出调试信息
    console.log('当前页面的所有按钮:', document.querySelectorAll('button'))
    console.log('当前页面的所有带role=tab的元素:', document.querySelectorAll('[role="tab"]'))
  }
}

/**
 * 切换到指定标签页
 */
export const switchToTab = (tabValue: string) => {
  // 尝试多种选择器
  const selectors = [
    `[value="${tabValue}"]`,
    `[data-value="${tabValue}"]`,
    `button[role="tab"][value="${tabValue}"]`
  ]

  let tab: HTMLElement | null = null

  for (const selector of selectors) {
    try {
      tab = document.querySelector(selector) as HTMLElement
      if (tab) break
    } catch (e) {
      // 忽略无效的选择器
    }
  }

  if (tab) {
    tab.click()
  } else {
    console.warn(`未找到标签页: ${tabValue}`)
  }
}

/**
 * 切换到指定标签页并滚动到指定元素
 */
export const switchToTabAndScroll = (tabValue: string, elementId: string, offset = 120) => {
  const tab = document.querySelector(`[value="${tabValue}"]`) as HTMLElement
  if (tab) {
    tab.click()
    setTimeout(() => {
      const element = document.getElementById(elementId)
      if (element) {
        const offsetTop = element.offsetTop - offset
        window.scrollTo({
          top: Math.max(0, offsetTop),
          behavior: 'smooth'
        })
      }
    }, 250)
  }
}
