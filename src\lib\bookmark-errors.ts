/**
 * 收藏模块错误处理工具
 * 
 * 提供统一的错误处理、用户友好的错误消息和错误恢复机制
 */

import { toast } from 'sonner';

/**
 * 收藏相关错误代码
 */
export const BOOKMARK_ERROR_CODES = {
  // 认证错误
  AUTHENTICATION_REQUIRED: 20001,
  INVALID_TOKEN: 20002,
  TOKEN_EXPIRED: 20003,
  
  // 业务错误
  INVALID_CIRCLE_ID: 40001,
  CIRCLE_NOT_FOUND: 10002,
  BOOKMARK_NOT_FOUND: 40002,
  DUPLICATE_BOOKMARK: 40003,
  
  // 系统错误
  SERVER_ERROR: 50001,
  DATABASE_ERROR: 50002,
  RATE_LIMIT_EXCEEDED: 42901,
} as const;

/**
 * 错误消息映射
 */
const ERROR_MESSAGES = {
  [BOOKMARK_ERROR_CODES.AUTHENTICATION_REQUIRED]: 'Please log in to bookmark circles',
  [BOOKMARK_ERROR_CODES.INVALID_TOKEN]: 'Your session has expired. Please log in again',
  [BOOKMARK_ERROR_CODES.TOKEN_EXPIRED]: 'Your session has expired. Please log in again',
  [BOOKMARK_ERROR_CODES.INVALID_CIRCLE_ID]: 'Invalid circle ID format',
  [BOOKMARK_ERROR_CODES.CIRCLE_NOT_FOUND]: 'Circle not found',
  [BOOKMARK_ERROR_CODES.BOOKMARK_NOT_FOUND]: 'Bookmark not found',
  [BOOKMARK_ERROR_CODES.DUPLICATE_BOOKMARK]: 'Circle is already bookmarked',
  [BOOKMARK_ERROR_CODES.SERVER_ERROR]: 'Server error. Please try again later',
  [BOOKMARK_ERROR_CODES.DATABASE_ERROR]: 'Database error. Please try again later',
  [BOOKMARK_ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please wait a moment',
} as const;

/**
 * HTTP 状态码错误消息
 */
const HTTP_ERROR_MESSAGES = {
  400: 'Invalid request format',
  401: 'Authentication required',
  403: 'Access denied',
  404: 'Resource not found',
  409: 'Conflict occurred',
  429: 'Too many requests',
  500: 'Internal server error',
  502: 'Service temporarily unavailable',
  503: 'Service temporarily unavailable',
  504: 'Request timeout',
} as const;

/**
 * 错误类型定义
 */
export interface BookmarkError {
  status?: number;
  code?: number;
  message?: string;
  payload?: {
    code?: number;
    message?: string;
    data?: any;
  };
}

/**
 * 获取用户友好的错误消息
 */
export function getBookmarkErrorMessage(error: BookmarkError): string {
  // 优先使用业务错误代码
  if (error.payload?.code && error.payload.code in ERROR_MESSAGES) {
    return ERROR_MESSAGES[error.payload.code as keyof typeof ERROR_MESSAGES];
  }
  
  // 使用 HTTP 状态码
  if (error.status && error.status in HTTP_ERROR_MESSAGES) {
    return HTTP_ERROR_MESSAGES[error.status as keyof typeof HTTP_ERROR_MESSAGES];
  }
  
  // 使用服务器返回的消息
  if (error.payload?.message) {
    return error.payload.message;
  }
  
  if (error.message) {
    return error.message;
  }
  
  // 默认错误消息
  return 'An unexpected error occurred. Please try again';
}

/**
 * 显示错误提示
 */
export function showBookmarkError(error: BookmarkError, action?: string): void {
  const message = getBookmarkErrorMessage(error);
  const title = action ? `${action} failed` : 'Operation failed';
  
  toast.error(title, {
    description: message,
    duration: 5000,
  });
}

/**
 * 显示成功提示
 */
export function showBookmarkSuccess(message: string, description?: string): void {
  toast.success(message, {
    description,
    duration: 3000,
  });
}

/**
 * 错误恢复建议
 */
export function getErrorRecoveryAction(error: BookmarkError): {
  action: string;
  handler?: () => void;
} | null {
  if (error.status === 401 || error.payload?.code === BOOKMARK_ERROR_CODES.AUTHENTICATION_REQUIRED) {
    return {
      action: 'Log in',
      handler: () => {
        // 重定向到登录页面
        window.location.href = '/login';
      },
    };
  }
  
  if (error.status === 429 || error.payload?.code === BOOKMARK_ERROR_CODES.RATE_LIMIT_EXCEEDED) {
    return {
      action: 'Wait and retry',
      handler: () => {
        // 等待一段时间后重试
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      },
    };
  }
  
  if (error.status && error.status >= 500) {
    return {
      action: 'Refresh page',
      handler: () => {
        window.location.reload();
      },
    };
  }
  
  return null;
}

/**
 * 错误重试逻辑
 */
export function shouldRetryBookmarkOperation(
  error: BookmarkError,
  attemptCount: number,
  maxAttempts: number = 3
): boolean {
  // 超过最大重试次数
  if (attemptCount >= maxAttempts) {
    return false;
  }
  
  // 认证错误不重试
  if (error.status === 401 || error.payload?.code === BOOKMARK_ERROR_CODES.AUTHENTICATION_REQUIRED) {
    return false;
  }
  
  // 客户端错误（4xx）不重试
  if (error.status && error.status >= 400 && error.status < 500) {
    return false;
  }
  
  // 服务器错误（5xx）可以重试
  if (error.status && error.status >= 500) {
    return true;
  }
  
  // 网络错误可以重试
  if (!error.status) {
    return true;
  }
  
  return false;
}

/**
 * 计算重试延迟（指数退避）
 */
export function getRetryDelay(attemptCount: number): number {
  const baseDelay = 1000; // 1秒
  const maxDelay = 10000; // 10秒
  
  const delay = Math.min(baseDelay * Math.pow(2, attemptCount), maxDelay);
  
  // 添加随机抖动
  const jitter = Math.random() * 0.1 * delay;
  
  return delay + jitter;
}

/**
 * 错误日志记录
 */
export function logBookmarkError(
  error: BookmarkError,
  context: {
    action: string;
    circleId?: string;
    userId?: string;
    timestamp?: Date;
  }
): void {
  const logData = {
    ...context,
    timestamp: context.timestamp || new Date(),
    error: {
      status: error.status,
      code: error.payload?.code,
      message: error.message || error.payload?.message,
    },
  };
  
  // 在开发环境中输出到控制台
  if (process.env.NODE_ENV === 'development') {
    console.error('Bookmark Error:', logData);
  }
  
  // 在生产环境中发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成 Sentry、LogRocket 等错误监控服务
    // sendErrorToMonitoring(logData);
  }
}
