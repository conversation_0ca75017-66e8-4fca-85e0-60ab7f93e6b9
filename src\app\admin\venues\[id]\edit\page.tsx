"use client";

import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { ArrowLeft, Loader2 } from "lucide-react";
import { useEffect, use } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import VenueForm from "@/components/admin/VenueForm";
import { MultilingualContentManager } from "@/components/rich-text-editor";
import { useAdminVenueDetail, useUpdateVenue } from "@/hooks/admin/useVenues";

interface VenueFormData {
  name_en: string;
  name_ja: string;
  name_zh: string;
  address_en?: string;
  address_ja?: string;
  address_zh?: string;
  lat: number;
  lng: number;
  capacity?: number;
  website_url?: string;
  phone?: string;
  description_en?: string;
  description_ja?: string;
  description_zh?: string;
  facilities?: string;
  transportation?: string;
  parking_info?: string;
}

interface EditVenuePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditVenuePage({ params }: EditVenuePageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { data: venue, isLoading, error } = useAdminVenueDetail(resolvedParams.id);
  const updateVenue = useUpdateVenue(resolvedParams.id);

  const form = useForm<VenueFormData>({
    defaultValues: {
      name_en: "",
      name_ja: "",
      name_zh: "",
      address_en: "",
      address_ja: "",
      address_zh: "",
      lat: 0,
      lng: 0,
      capacity: undefined,
      website_url: "",
      phone: "",
      description_en: "",
      description_ja: "",
      description_zh: "",
      facilities: "",
      transportation: "",
      parking_info: "",
    },
  });

  // 当venue数据加载完成时，更新表单默认值
  useEffect(() => {
    if (venue) {
      form.reset({
        name_en: venue.name_en || "",
        name_ja: venue.name_ja || "",
        name_zh: venue.name_zh || "",
        address_en: venue.address_en || "",
        address_ja: venue.address_ja || "",
        address_zh: venue.address_zh || "",
        lat: venue.lat || 0,
        lng: venue.lng || 0,
        capacity: venue.capacity || undefined,
        website_url: venue.website_url || "",
        phone: venue.phone || "",
        description_en: venue.description_en || "",
        description_ja: venue.description_ja || "",
        description_zh: venue.description_zh || "",
        facilities: venue.facilities || "",
        transportation: venue.transportation || "",
        parking_info: venue.parking_info || "",
      });
    }
  }, [venue, form]);

  const onSubmit = (values: VenueFormData) => {
    updateVenue.mutate(values);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold">编辑场馆</h1>
          </div>
        </div>
        
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            加载中...
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !venue) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold">编辑场馆</h1>
          </div>
        </div>
        
        <Card>
          <CardContent className="text-center py-12">
            <p className="text-destructive">场馆不存在或加载失败</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => router.push("/admin/venues")}
            >
              返回场馆列表
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和导航 */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回
        </Button>
        <div>
          <h1 className="text-2xl font-bold">编辑场馆</h1>
          <p className="text-muted-foreground text-sm">
            编辑 {venue.name_zh || venue.name_en} 的信息
          </p>
        </div>
      </div>

      {/* 表单 */}
      <VenueForm
        form={form}
        onSubmit={onSubmit}
        isSubmitting={updateVenue.isPending}
        submitText="保存更改"
        title="编辑场馆信息"
        description="修改场馆的多语言信息和详细资料"
      />

      {/* 富文本内容编辑器 */}
      <div className="space-y-4">
        <div>
          <h2 className="text-xl font-semibold">场馆详细信息</h2>
          <p className="text-muted-foreground text-sm">
            编辑场馆的多语言富文本内容，包括介绍、亮点、指南和注意事项
          </p>
        </div>

        <MultilingualContentManager
          entityType="venue"
          entityId={resolvedParams.id}
          className="w-full"
        />
      </div>
    </div>
  );
}
