'use client'

import { useTranslations } from 'next-intl'
import { Calendar, MapPin, Users, Clock, ExternalLink } from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import EventPoster from './EventPoster'
import EventMap from './EventMap'
import { createVenueFromEvent, type EventHeaderProps } from './types'
import { scrollToVenueMap } from './navigationUtils'

/**
 * 增强版事件头部组件
 * 
 * 新增功能：
 * - 英雄区域设计
 * - 快速信息卡片
 * - 社交功能按钮
 * - 更好的视觉层次
 */
export default function EnhancedEventHeader({
  event,
  isLoading = false,
  error = null,
  className,
  circlesCount = 0
}: EventHeaderProps & { circlesCount?: number }) {
  const t = useTranslations('EventHeader')
  const venue = createVenueFromEvent(event)

  if (!event) {
    return (
      <header className={cn("bg-background text-foreground", className)}>
        <div className="max-w-7xl mx-auto px-4 py-10">
          {/* 骨架屏 */}
          <div className="space-y-6">
            <div className="h-12 bg-gray-200 rounded animate-pulse" />
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded animate-pulse" />
              ))}
            </div>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header className={cn("bg-background text-foreground", className)}>
      {/* 英雄区域 */}
      <div className="relative hero-gradient">
        <div className="max-w-7xl mx-auto px-4 py-8 md:py-12">
          <div className="grid grid-cols-1 lg:grid-cols-[minmax(300px,400px)_1fr] gap-6 md:gap-8 lg:gap-12 items-start">
            {/* 左侧：事件海报 */}
            <div className="flex justify-center lg:justify-start">
              <div className="w-full max-w-sm lg:max-w-none">
                <EventPoster
                  imageUrl={event.image_url}
                  eventName={event.name}
                  eventId={event.id}
                  className="w-full"
                />
              </div>
            </div>

            {/* 右侧：事件信息 */}
            <div className="space-y-4 md:space-y-6 text-center lg:text-left">
              {/* 状态徽章 */}
              <div className="flex justify-center lg:justify-start">
                <Badge variant="secondary" className="text-sm">
                  即将举办
                </Badge>
              </div>

              {/* 事件标题 */}
              <div className="space-y-2">
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
                  {event.name}
                </h1>
                <p className="text-lg md:text-xl text-muted-foreground">
                  {event.date} • {event.venue_name}
                </p>
              </div>

              {/* 描述 */}
              {event.description && (
                <p className="text-base md:text-lg text-muted-foreground leading-relaxed max-w-2xl mx-auto lg:mx-0">
                  {event.description}
                </p>
              )}

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                {event.url && (
                  <Button size="lg" className="button-enhanced" asChild>
                    <a href={event.url} target="_blank" rel="noopener noreferrer">
                      官方网站
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 快速信息卡片 */}
      <div className="border-b bg-background/95 backdrop-blur">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <InfoCard
              icon={<Calendar className="h-5 w-5" />}
              label={t('date')}
              value={event.date || '待定'}
            />
            <InfoCard
              icon={<MapPin className="h-5 w-5" />}
              label={t('venue')}
              value={event.venue_name || '待定'}
              isClickable={true}
              onClick={scrollToVenueMap}
            />
            <InfoCard
              icon={<Users className="h-5 w-5" />}
              label="参展商"
              value={`${circlesCount}+`}
            />
            <InfoCard
              icon={<Clock className="h-5 w-5" />}
              label="开放时间"
              value="10:00 - 18:00"
            />
          </div>
        </div>
      </div>
    </header>
  )
}

/**
 * 信息卡片组件
 */
function InfoCard({
  icon,
  label,
  value,
  isClickable = false,
  onClick
}: {
  icon: React.ReactNode
  label: string
  value: string
  isClickable?: boolean
  onClick?: () => void
}) {
  const baseClasses = "info-card flex items-center space-x-3 p-4 rounded-lg"
  const clickableClasses = isClickable
    ? "cursor-pointer hover:bg-muted/50 transition-colors border border-transparent hover:border-primary/20"
    : ""

  return (
    <div
      className={`${baseClasses} ${clickableClasses}`}
      onClick={isClickable ? onClick : undefined}
    >
      <div className="text-primary">
        {icon}
      </div>
      <div className="min-w-0 flex-1">
        <p className="text-sm text-muted-foreground">{label}</p>
        <p className="font-medium truncate">{value}</p>
        {isClickable && (
          <p className="text-xs text-primary mt-1">点击查看地图</p>
        )}
      </div>
    </div>
  )
}
