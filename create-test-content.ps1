# 创建测试富文本内容
$API_BASE = "http://127.0.0.1:9830"
$EVENT_ID = "785e0d4c-9dae-4fe7-8c4b-a503ffc14248"

# 测试内容
$testContent = @{
    introduction = "<h2>活动介绍</h2><p>这是一个精彩的<strong>同人展会</strong>活动！</p><p>欢迎所有同人爱好者参与。</p>"
    highlights = "<h3>活动亮点</h3><ul><li>🎨 精美的同人作品展示</li><li>🎪 互动体验区</li><li>🎁 限定商品销售</li><li>📸 与创作者面对面交流</li></ul>"
    guide = "<h3>参与指南</h3><ol><li><strong>提前注册</strong>：请在官网完成注册</li><li><strong>准时到达</strong>：活动准时开始，请勿迟到</li><li><strong>遵守规则</strong>：请遵守会场规定</li><li><strong>享受活动</strong>：尽情享受同人文化的魅力！</li></ol>"
    notices = "<h3>重要通知</h3><div style='background-color: #fef2f2; padding: 16px; border-left: 4px solid #ef4444; margin: 16px 0;'><p><strong>⚠️ 注意事项：</strong></p><ul><li>请携带有效身份证件</li><li>禁止携带危险物品</li><li>会场内禁止吸烟</li><li>请保持会场清洁</li></ul></div>"
} | ConvertTo-Json -Depth 10

Write-Host "🧪 创建测试富文本内容..." -ForegroundColor Cyan

try {
    # 创建内容
    $response = Invoke-WebRequest -Uri "$API_BASE/event/$EVENT_ID/content" -Method PUT -Body $testContent -ContentType "application/json"
    
    Write-Host "✅ 内容创建成功！" -ForegroundColor Green
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Yellow
    Write-Host "响应: $($response.Content)" -ForegroundColor Gray
    
    Write-Host "`n📝 下一步操作：" -ForegroundColor Cyan
    Write-Host "1. 打开浏览器访问: http://localhost:3003/events/$EVENT_ID" -ForegroundColor White
    Write-Host "2. 点击 '详细信息' 标签页查看富文本内容" -ForegroundColor White
    Write-Host "3. 访问管理页面: http://localhost:3003/admin/events/$EVENT_ID/edit" -ForegroundColor White
    Write-Host "4. 在 '内容管理' 部分编辑富文本内容" -ForegroundColor White
    
} catch {
    Write-Host "❌ 创建内容失败：$($_.Exception.Message)" -ForegroundColor Red
    Write-Host "错误详情：$($_.ErrorDetails.Message)" -ForegroundColor Red
}
