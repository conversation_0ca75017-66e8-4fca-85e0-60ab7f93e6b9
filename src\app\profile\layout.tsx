'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/user';
import { useRouter } from 'next/navigation';
import { UserCenterLayout } from '@/components/profile/UserCenterLayout';

interface ProfileLayoutProps {
  children: React.ReactNode;
}

export default function ProfileLayout({ children }: ProfileLayoutProps) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // 等待认证状态加载完成后再判断
    if (!isLoading && !isAuthenticated) {
      // 如果用户未登录，重定向到登录页面
      router.push('/login?redirect=/profile');
    }
  }, [isAuthenticated, isLoading, router]);

  // 在认证状态加载中时显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">Loading...</p>
        </div>
      </div>
    );
  }

  // 如果用户未登录，显示空白页面（重定向正在进行中）
  if (!isAuthenticated) {
    return null;
  }

  return (
    <UserCenterLayout user={user}>
      {children}
    </UserCenterLayout>
  );
}
