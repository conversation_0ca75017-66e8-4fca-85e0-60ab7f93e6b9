/**
 * Ayafeed 设计系统组件库
 * 
 * 这个文件包含了基于设计系统的可复用组件
 * 所有组件都遵循 Ayafeed 的现代艺术风格设计原则
 */

import { motion } from 'motion/react'
import { ReactNode } from 'react'

// ============================================================================
// 布局组件
// ============================================================================

/**
 * 现代页面布局组件
 * 提供标准的渐变背景和装饰元素
 */
interface ModernPageLayoutProps {
  children: ReactNode
  className?: string
}

export function ModernPageLayout({ children, className = '' }: ModernPageLayoutProps) {
  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden ${className}`}>
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/5 to-blue-600/5 rounded-full blur-3xl"></div>
      </div>
      
      <div className="relative">
        {children}
      </div>
    </div>
  )
}

/**
 * 容器组件
 * 提供标准的最大宽度和内边距
 */
interface ContainerProps {
  children: ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

export function Container({ children, className = '', size = 'xl' }: ContainerProps) {
  const sizeClasses = {
    sm: 'max-w-3xl',
    md: 'max-w-5xl', 
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  }
  
  return (
    <div className={`${sizeClasses[size]} mx-auto px-4 md:px-6 lg:px-8 ${className}`}>
      {children}
    </div>
  )
}

// ============================================================================
// 卡片组件
// ============================================================================

/**
 * 现代卡片组件
 * 包含毛玻璃效果、渐变背景和悬停动画
 */
interface ModernCardProps {
  children: ReactNode
  className?: string
  hover?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export function ModernCard({ children, className = '', hover = true, padding = 'md' }: ModernCardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }
  
  return (
    <motion.div
      className={`overflow-hidden border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/80 to-slate-50/80 dark:from-slate-900/80 dark:to-slate-800/80 backdrop-blur-xl rounded-xl transition-all duration-300 ${hover ? 'hover:shadow-2xl hover:shadow-blue-500/10' : ''} ${paddingClasses[padding]} ${className}`}
      whileHover={hover ? { y: -8, scale: 1.02 } : undefined}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  )
}

/**
 * 图片卡片组件
 * 专门用于展示图片内容的卡片
 */
interface ImageCardProps {
  children: ReactNode
  className?: string
  imageClassName?: string
}

export function ImageCard({ children, className = '', imageClassName = '' }: ImageCardProps) {
  return (
    <ModernCard className={`group ${className}`} padding="none">
      <div className={`relative overflow-hidden ${imageClassName}`}>
        {children}
        {/* 悬停遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        {/* 装饰性渐变边框 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ mixBlendMode: 'overlay' }} />
      </div>
    </ModernCard>
  )
}

// ============================================================================
// 文字组件
// ============================================================================

/**
 * 渐变标题组件
 * 提供统一的渐变文字效果
 */
interface GradientHeadingProps {
  children: ReactNode
  level?: 1 | 2 | 3 | 4 | 5 | 6
  className?: string
  gradient?: 'default' | 'brand' | 'accent'
}

export function GradientHeading({ children, level = 1, className = '', gradient = 'default' }: GradientHeadingProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements
  
  const sizeClasses = {
    1: 'text-4xl',
    2: 'text-3xl',
    3: 'text-2xl',
    4: 'text-xl',
    5: 'text-lg',
    6: 'text-base'
  }
  
  const gradientClasses = {
    default: 'from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400',
    brand: 'from-blue-600 to-purple-600',
    accent: 'from-purple-600 to-pink-600'
  }
  
  return (
    <Tag className={`font-bold bg-gradient-to-r ${gradientClasses[gradient]} bg-clip-text text-transparent ${sizeClasses[level]} ${className}`}>
      {children}
    </Tag>
  )
}

/**
 * 副标题组件
 */
interface SubtitleProps {
  children: ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function Subtitle({ children, className = '', size = 'md' }: SubtitleProps) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }
  
  return (
    <p className={`text-slate-600 dark:text-slate-400 ${sizeClasses[size]} ${className}`}>
      {children}
    </p>
  )
}

// ============================================================================
// 表单组件
// ============================================================================

/**
 * 现代输入框组件
 */
interface ModernInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
}

export function ModernInput({ className = '', ...props }: ModernInputProps) {
  return (
    <input
      className={`bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 focus:ring-2 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-300 rounded-xl px-4 py-2 w-full ${className}`}
      {...props}
    />
  )
}

/**
 * 现代按钮组件
 */
interface ModernButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  children: ReactNode
}

export function ModernButton({ 
  variant = 'primary', 
  size = 'md', 
  className = '', 
  children, 
  ...props 
}: ModernButtonProps) {
  const baseClasses = 'backdrop-blur-sm transition-all duration-300 rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-blue-500/20'
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl',
    secondary: 'bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 hover:bg-white/80 dark:hover:bg-slate-800/80',
    outline: 'border border-slate-200/50 dark:border-slate-700/50 hover:bg-slate-50/50 dark:hover:bg-slate-800/50',
    ghost: 'hover:bg-slate-100/50 dark:hover:bg-slate-800/50'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg'
  }
  
  return (
    <motion.button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
      {...props}
    >
      {children}
    </motion.button>
  )
}

// ============================================================================
// 网格组件
// ============================================================================

/**
 * 响应式网格组件
 */
interface ResponsiveGridProps {
  children: ReactNode
  className?: string
  cols?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: number
}

export function ResponsiveGrid({ 
  children, 
  className = '', 
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = 6 
}: ResponsiveGridProps) {
  const gridClasses = [
    `grid-cols-${cols.default || 1}`,
    cols.sm && `sm:grid-cols-${cols.sm}`,
    cols.md && `md:grid-cols-${cols.md}`,
    cols.lg && `lg:grid-cols-${cols.lg}`,
    cols.xl && `xl:grid-cols-${cols.xl}`,
    `gap-${gap}`
  ].filter(Boolean).join(' ')
  
  return (
    <div className={`grid ${gridClasses} ${className}`}>
      {children}
    </div>
  )
}

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  // 布局
  ModernPageLayout,
  Container,
  
  // 卡片
  ModernCard,
  ImageCard,
  
  // 文字
  GradientHeading,
  Subtitle,
  
  // 表单
  ModernInput,
  ModernButton,
  
  // 网格
  ResponsiveGrid
}
