# 富文本编辑器标签页管理 - 后端扩展文档

> 📝 **版本**: v2.0.0
> 🕒 **更新时间**: 2025-01-02
> 👥 **目标读者**: 后端开发者
> 🎯 **功能**: 为富文本编辑器添加动态标签页管理功能

## 📋 目录

- [概述](#概述)
- [数据库设计](#数据库设计)
- [API 设计](#api-设计)
- [删除策略](#删除策略)
- [缓存优化](#缓存优化)
- [实现步骤](#实现步骤)
- [安全考虑](#安全考虑)
- [测试策略](#测试策略)

## 概述

### 功能目标

为富文本编辑器添加动态标签页管理功能，允许管理员通过后台界面：

- 添加新的内容类型标签页
- 删除/禁用不需要的标签页（支持软删除）
- 修改标签页配置（名称、图标、排序等）
- 启用/禁用特定标签页
- 恢复已删除的标签页

### 技术要求

- **向后兼容**: 保持现有 API 的兼容性
- **权限控制**: 仅 admin/editor 角色可管理
- **数据安全**: 软删除机制，支持数据恢复
- **性能优化**: 智能缓存和批量操作
- **操作审计**: 完整的操作日志记录

## 数据库设计

### 增强的表结构设计

**标签页配置表**采用软删除机制，支持数据恢复和审计：

- **主要字段**: id, key, label, icon, placeholder, sort_order
- **状态控制**: is_active（启用/禁用）, is_system（系统保护）
- **软删除**: deleted_at, deleted_by（删除时间和操作者）
- **审计字段**: created_at, updated_at

**关键约束**:

- key 字段唯一性约束，支持字母、数字、下划线、连字符
- label 长度限制 1-100 字符
- sort_order 非负整数

**优化索引**:

- 活跃类型查询索引：`(is_active, sort_order) WHERE is_active = true`
- 键值查询索引：`(key, is_active)`

### 简化的数据迁移策略

**重要变更**: 由于现有内容为随机填充数据，采用直接清理策略：

1. **清空现有数据**: 删除 rich_text_contents 表中的所有随机数据
2. **移除硬编码约束**: 重建表结构，移除 content_type 的 CHECK 约束
3. **创建配置表**: 建立新的 content_type_configs 表
4. **插入默认配置**: 添加系统预设的4种内容类型

**迁移优势**:

- 无需复杂的数据转换
- 降低迁移风险
- 简化实施流程
- 保证数据一致性

## API 设计

### 增强的路由结构

```
/admin/rich-text/content-types/
├── GET    /                    # 获取所有标签页配置（支持过滤）
├── POST   /                    # 创建新标签页
├── GET    /:id                 # 获取单个标签页配置
├── PUT    /:id                 # 更新标签页配置
├── DELETE /:id                 # 删除标签页（支持软删除/硬删除）
├── PATCH  /:id/restore         # 恢复已删除的标签页
├── PUT    /reorder             # 批量更新排序
└── PATCH  /batch-status        # 批量更新状态（启用/禁用）
```

### 查询参数支持

- `GET /` 支持 `?active=true` 过滤活跃标签页
- `DELETE /:id` 支持 `?hard=true&cascade=true` 控制删除行为

### 核心数据模型

**ContentTypeConfig 主要字段**:

- `id`: 唯一标识符
- `key`: 标签页键值（如 'introduction'）
- `label`: 显示名称（如 '介绍'）
- `placeholder`: 输入提示文本
- `icon`: 图标名称（Lucide 图标）
- `sort_order`: 排序权重
- `is_active`: 启用状态
- `is_system`: 系统保护标记
- `deleted_at`: 软删除时间戳
- `deleted_by`: 删除操作者

**请求/响应模型**:

- `CreateContentTypeConfigRequest`: 创建标签页请求
- `UpdateContentTypeConfigRequest`: 更新标签页请求（部分字段）
- `ReorderContentTypesRequest`: 批量排序请求
- `BatchStatusUpdateRequest`: 批量状态更新请求

**验证规则**:

- key: 2-50字符，仅支持字母、数字、下划线、连字符
- label: 1-100字符，必填
- sort_order: 非负整数
- 系统预设类型的 key 不可修改

## 删除策略

### 软删除机制设计

**推荐策略**: 采用软删除 + is_active 双重保护机制

**删除级别**:

1. **禁用**: 设置 `is_active = false`，标签页不显示但数据保留
2. **软删除**: 设置 `deleted_at` 时间戳，标记为已删除状态
3. **硬删除**: 物理删除数据库记录（仅限非系统类型）

**关联数据处理**:

- **保守策略**: 保留关联的富文本内容，仅在前端过滤显示
- **级联策略**: 可选择同时隐藏关联内容（通过 cascade 参数控制）
- **系统保护**: 系统预设类型仅支持禁用，不支持删除

**恢复机制**:

- 支持恢复软删除的标签页
- 恢复时自动清除 `deleted_at` 和 `deleted_by` 字段
- 可选择是否同时恢复关联内容的显示状态

### 删除操作流程

1. **权限验证**: 确认操作者具有 admin/editor 权限
2. **系统保护检查**: 验证非系统预设类型
3. **关联数据评估**: 检查是否存在关联的富文本内容
4. **用户确认**: 前端提供删除模式选择（软删除/硬删除/级联）
5. **执行删除**: 根据选择执行相应的删除操作
6. **操作审计**: 记录详细的操作日志
7. **缓存失效**: 清除相关缓存数据

## 缓存优化

### 智能缓存策略

**缓存层级**:

- **L1 缓存**: 内存缓存活跃标签页配置（5分钟 TTL）
- **L2 缓存**: 完整配置列表缓存（按需刷新）
- **查询优化**: 活跃类型专用索引，提升查询性能

**缓存失效机制**:

- 配置变更时自动失效相关缓存
- 支持手动缓存刷新
- 批量操作时延迟失效，避免频繁刷新

**性能优化**:

- 活跃类型查询优先使用缓存
- 管理界面查询直接访问数据库
- 支持缓存预热和后台刷新

## 实现步骤

### 简化的实施计划

**总体时间**: 3-4 天（相比原计划减少 2-3 天）

### 阶段 1: 数据库重构 (半天)

**任务清单**:

1. **清空现有数据**: 删除 rich_text_contents 表中的随机填充数据
2. **重建表结构**: 移除 content_type 的硬编码 CHECK 约束
3. **创建配置表**: 建立 content_type_configs 表及索引
4. **插入默认数据**: 添加4种系统预设内容类型

**风险控制**:

- 数据库操作前进行完整备份
- 在测试环境先行验证
- 准备回滚脚本

### 阶段 2: 核心功能开发 (1.5 天)

**开发重点**:

1. **Repository 层**: 实现基础 CRUD 和软删除逻辑
2. **Service 层**: 业务逻辑、缓存机制、权限验证
3. **Controller 层**: HTTP 接口处理、错误处理
4. **Routes 层**: API 路由定义、OpenAPI 文档

**关键特性**:

- 软删除和恢复功能
- 智能缓存机制
- 批量操作支持
- 系统类型保护

### 阶段 3: 增强功能 (1 天)

**功能完善**:

1. **删除策略**: 实现多级删除机制（禁用/软删除/硬删除）
2. **恢复机制**: 支持已删除标签页的恢复
3. **批量操作**: 状态批量更新、排序优化
4. **操作审计**: 完整的操作日志记录

### 阶段 4: 集成测试 (1 天)

**测试重点**:

1. **功能测试**: 验证所有 CRUD 操作
2. **权限测试**: 确认角色权限控制
3. **性能测试**: 缓存效果和查询性能
4. **安全测试**: 输入验证和 XSS 防护

**部署准备**:

- API 文档更新
- 前端集成指南
- 运维监控配置

## 安全考虑

### 增强的安全策略

**输入验证强化**:

- 使用 Zod 进行严格的类型检查和格式验证
- key 字段仅允许字母、数字、下划线、连字符
- 长度限制和特殊字符过滤
- 防止 SQL 注入和 XSS 攻击

**权限控制细化**:

- 基于角色的访问控制（admin/editor）
- 系统预设类型的特殊保护机制
- 操作级别的权限验证
- API 密钥和会话管理

**数据完整性保障**:

- 软删除机制防止数据丢失
- 关联数据的一致性检查
- 事务性操作确保原子性
- 定期数据完整性验证

**操作审计完善**:

- 详细的操作日志记录
- 包含操作者、时间、详细变更内容
- 敏感操作的额外审计
- 日志数据的安全存储

### 安全最佳实践

**开发阶段**:

- 代码审查和安全扫描
- 依赖项安全检查
- 单元测试覆盖安全场景

**部署阶段**:

- 环境变量管理
- 数据库连接安全
- API 访问频率限制

**运维阶段**:

- 定期安全更新
- 监控异常操作
- 备份和恢复策略

## 测试策略

### 全面的测试覆盖

**单元测试重点**:

- Repository 层数据操作逻辑
- Service 层业务规则验证
- 软删除和恢复机制
- 缓存失效和更新逻辑
- 权限验证和系统保护

**集成测试场景**:

- 完整的 CRUD 操作流程
- 批量操作的事务性
- 权限控制的端到端验证
- 错误处理和异常恢复
- 性能基准和压力测试

**安全测试验证**:

- 输入验证和边界条件
- SQL 注入和 XSS 防护
- 权限绕过尝试
- 恶意数据处理

### 测试数据管理

**测试环境隔离**:

- 独立的测试数据库
- 可重复的测试数据集
- 自动化的数据清理

**性能基准**:

- 查询响应时间 < 100ms
- 批量操作 < 1s
- 缓存命中率 > 90%
- 并发处理能力验证

## 📚 相关文档

- [富文本编辑器标签页管理 - 前端扩展文档](./rich-text-tabs-frontend-extension.md)
- [富文本模块前端对接文档](../富文本模块前端对接文档.md)
- [API 开发规范](./api-development-guidelines.md)
- [数据库迁移指南](./database-migration-guide.md)

---

## 🔄 更新日志

- **v2.0.0** (2025-01-02): 重大改进版本
  - 采用软删除机制，提升数据安全性
  - 简化数据迁移策略，降低实施风险
  - 增强缓存策略，优化查询性能
  - 完善删除和恢复机制
  - 强化安全控制和操作审计

- **v1.0.0** (2025-01-02): 初始版本
  - 基础的动态标签页管理功能
  - 完整的 CRUD 接口设计
  - 权限控制和系统保护机制

---

**💡 实施建议**: 这是一个经过优化的技术方案，在数据安全性、实施复杂性和性能表现之间取得了良好平衡。建议按照分阶段策略实施，确保每个阶段的质量和稳定性。

## 📚 相关文档

- [富文本编辑器标签页管理 - 前端扩展文档](./rich-text-tabs-frontend-extension.md)
- [富文本模块前端对接文档](../富文本模块前端对接文档.md)
- [API 开发规范](./api-development-guidelines.md)
- [数据库迁移指南](./database-migration-guide.md)

---

## 🔄 更新日志

- **v2.0.0** (2025-01-02): 重大改进版本
  - 采用软删除机制，提升数据安全性
  - 简化数据迁移策略，降低实施风险
  - 增强缓存策略，优化查询性能
  - 完善删除和恢复机制
  - 强化安全控制和操作审计

- **v1.0.0** (2025-01-02): 初始版本
  - 基础的动态标签页管理功能
  - 完整的 CRUD 接口设计
  - 权限控制和系统保护机制

---

**💡 实施建议**: 这是一个经过优化的技术方案，在数据安全性、实施复杂性和性能表现之间取得了良好平衡。建议按照分阶段策略实施，确保每个阶段的质量和稳定性。
