'use client';

import React from 'react';
import { BookmarkList } from '@/components/bookmark';
import { useBookmarks, useBookmarkStats } from '@/hooks/useBookmark';
import { BookmarkListSkeleton, BookmarkStatsSkeleton } from '@/components/bookmark/BookmarkSkeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Heart, TrendingUp, Calendar } from 'lucide-react';

export default function BookmarksPage() {
  // 同时获取收藏列表和统计数据
  const { data: bookmarksData, isLoading: bookmarksLoading } = useBookmarks();
  const { data: statsData, isLoading: statsLoading } = useBookmarkStats();

  // 从收藏列表数据中计算统计信息，减少对统计接口的依赖
  const calculatedStats = React.useMemo(() => {
    if (!bookmarksData?.data?.items) return null;

    const items = bookmarksData.data.items;
    const total = items.length;
    const thisMonth = items.filter(item => {
      const createdAt = new Date(item.createdAt);
      const now = new Date();
      return createdAt.getMonth() === now.getMonth() &&
             createdAt.getFullYear() === now.getFullYear();
    }).length;

    return { total, thisMonth };
  }, [bookmarksData]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* 页面标题 */}
        <header className="mb-8 text-center space-y-4">
          <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
            My Bookmarks
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            Manage and explore your bookmarked circles
          </p>
        </header>

        {/* 优化的统计信息 - 优先使用计算的统计，fallback到API统计 */}
        <div className="mb-8">
          {bookmarksLoading ? (
            <BookmarkStatsSkeleton variant="compact" />
          ) : (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  Bookmark Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                      <Heart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Total Bookmarks</p>
                      <p className="text-2xl font-bold">
                        {calculatedStats?.total ?? statsData?.data?.total ?? 0}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                      <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">This Month</p>
                      <p className="text-2xl font-bold">
                        {calculatedStats?.thisMonth ?? statsData?.data?.thisMonth ?? 0}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                      <TrendingUp className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Growth</p>
                      <p className="text-2xl font-bold">
                        {statsData?.data?.trend === 'up' ? '+' : ''}
                        {statsData?.data?.growth ?? 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 收藏列表 */}
        <main>
          <BookmarkList showHeader={false} />
        </main>
      </div>
    </div>
  );
}
