/**
 * 事件详情页面的数据转换工具函数
 */

import { useLocale } from "next-intl"
import type {
  GetEventsIdResponse,
  GetEventsIdCirclesResponse,
  GetAppearancesResponse
} from "@/api/generated/ayafeedComponents"
import type { Event } from "@/schemas/event"

/**
 * 将 API 返回的事件数据转换为本地 Event 类型
 */
export function transformEventData(apiEvent: GetEventsIdResponse): Event {
  return {
    id: apiEvent.id,
    name: apiEvent.name_zh || apiEvent.name_ja || apiEvent.name_en || "",
    date: apiEvent.date_zh || apiEvent.date_ja || apiEvent.date_en || "",
    date_sort: apiEvent.date_sort || 0,
    image_url: apiEvent.image_url || undefined,
    venue_name: undefined, // venue信息需要通过venue_id单独获取
    venue_address: undefined, // venue信息需要通过venue_id单独获取
    venue_lat: undefined, // venue信息需要通过venue_id单独获取
    venue_lng: undefined, // venue信息需要通过venue_id单独获取
    url: apiEvent.url || undefined,
    description: undefined, // GetEventsIdResponse 没有 description 字段
    created_at: apiEvent.created_at ? new Date(apiEvent.created_at) : undefined,
    updated_at: apiEvent.updated_at ? new Date(apiEvent.updated_at) : undefined,
  }
}

/**
 * 根据当前语言选择合适的字段值
 */
export function getLocalizedField(
  fields: {
    zh?: string | null
    ja?: string | null
    en?: string | null
  },
  locale: string
): string | undefined {
  // 优先使用当前语言
  if (locale === 'zh' && fields.zh) return fields.zh
  if (locale === 'ja' && fields.ja) return fields.ja
  if (locale === 'en' && fields.en) return fields.en
  
  // 回退到其他语言
  return fields.zh || fields.ja || fields.en || undefined
}

/**
 * 改进的事件数据转换函数，支持语言选择
 */
export function transformEventDataWithLocale(apiEvent: GetEventsIdResponse, locale: string): Event {
  // 优先使用多语言字段，如果不存在则回退到简单字段
  const getName = () => {
    const localizedName = getLocalizedField({
      zh: (apiEvent as any).name_zh,
      ja: (apiEvent as any).name_ja,
      en: (apiEvent as any).name_en
    }, locale);
    return localizedName || (apiEvent as any).name || "";
  };

  // venue信息需要通过venue_id单独获取，这里暂时返回undefined
  const getVenueName = () => {
    return undefined;
  };

  const getVenueAddress = () => {
    return undefined;
  };

  return {
    id: apiEvent.id,
    name: getName(),
    date: (apiEvent as any).date || "",
    date_sort: apiEvent.date_sort || 0,
    image_url: apiEvent.image_url || undefined,
    venue_name: getVenueName(),
    venue_address: getVenueAddress(),
    venue_lat: undefined, // venue信息需要通过venue_id单独获取
    venue_lng: undefined, // venue信息需要通过venue_id单独获取
    url: apiEvent.url || undefined,
    description: undefined, // 后端 API 中没有 description 字段
    created_at: apiEvent.created_at ? new Date(apiEvent.created_at) : undefined,
    updated_at: apiEvent.updated_at ? new Date(apiEvent.updated_at) : undefined,
  }
}

/**
 * 社团数据类型定义
 */
export interface TransformedCircle {
  id: string
  circle_id: string
  circle_name: string
  artist_name: string | null
  artist_id: string
  booth_id: string
  circle_urls: string
  [key: string]: any // 保留其他字段
}

/**
 * 转换社团数据，统一字段名称
 */
export function transformCirclesData(
  circlesData: GetEventsIdCirclesResponse,
  appearancesData: GetAppearancesResponse['items']
): TransformedCircle[] {
  // 创建 booth_id 映射
  const boothMap = new Map<string, string>()
  appearancesData?.forEach((appearance) => {
    if (appearance?.circle_id && appearance?.booth_id) {
      boothMap.set(appearance.circle_id, appearance.booth_id)
    }
  })

  return (circlesData || []).map((circle) => ({
    ...circle,
    id: circle.id,
    circle_id: circle.id, // 添加circle_id字段
    circle_name: (circle as any).name || "", // 使用name字段，而不是多语言字段
    artist_name: null, // GetEventsIdCirclesResponse 没有 artist_name 字段
    artist_id: "", // 添加artist_id字段，暂时为空
    booth_id: (circle as any).booth_id || boothMap.get(circle.id) || "",
    circle_urls: (circle as any).urls || "{}", // 使用circle.urls字段
  }))
}

/**
 * 过滤社团数据
 */
export function filterCircles(
  circles: TransformedCircle[],
  keyword: string
): TransformedCircle[] {
  return circles.filter((circle) => {
    // 关键字过滤
    if (keyword) {
      const searchKeyword = keyword.toLowerCase()
      const searchFields = [
        circle.circle_name,
        circle.artist_name,
        circle.booth_id
      ].filter(Boolean) as string[]

      const hasMatch = searchFields.some(field =>
        field.toLowerCase().includes(searchKeyword)
      )

      if (!hasMatch) {
        return false
      }
    }

    return true
  })
}


