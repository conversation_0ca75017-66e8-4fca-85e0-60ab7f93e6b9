import { OpenAPIHono } from '@hono/zod-openapi';
import { cors } from 'hono/cors';
import { trimTrailingSlash } from 'hono/trailing-slash';

import { betterAuthMiddleware } from './middlewares/betterAuth';
import { injectDependencies } from './middlewares/dependencies';
import { localeMiddleware } from './middlewares/locale';
import {
  adminRateLimit,
  publicApiRateLimit,
  searchRateLimit,
} from './config/rateLimits';
import { requestIdMiddleware } from './middlewares/requestId';
import { routes as admin } from './modules/admin';
import { routes as appearanceRoutes } from './modules/appearance';
import { routes as artistRoutes } from './modules/artist';
// import { routes as auth } from './modules/auth'; // 已被 Better Auth 替代
import { circleBookmarkRoutes, userBookmarkRoutes } from './modules/bookmark';
import { routes as circleRoutes } from './modules/circle';
import richTextRoutes from './modules/rich-text/routes';
import { routes as eventRoutes } from './modules/event';
import { routes as feedRoutes } from './modules/feed';
import { routes as imageRoutes } from './modules/images';
import { routes as searchRoutes } from './modules/search';
import { pubVenues } from './modules/venue/routes';
import { auth as betterAuth } from './lib/better-auth';

// 顶层应用实例（统一 OpenAPIHono）
const app = new OpenAPIHono();

// ------- 全局中间件 -------
app.use('*', requestIdMiddleware());
app.use(
  '*',
  cors({
    origin: (origin) => {
      if (!origin) return 'http://localhost:3000';
      const allowList = [
        'http://localhost:3000',
        'https://admin.example.com',
        'http://localhost:3001',
        'http://localhost:3002',
      ];
      return allowList.includes(origin) ? origin : 'http://localhost:3000';
    },
    allowHeaders: ['Content-Type', 'Authorization', 'X-Locale'],
    allowMethods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    credentials: true,
  })
);
app.use('*', trimTrailingSlash());
app.use('*', localeMiddleware);
app.use('*', betterAuthMiddleware());
app.use('*', injectDependencies);
// 应用基于角色的速率限制
// Better Auth 使用内置的 rate limiting，无需额外配置

// 管理员接口：基于角色的动态限制
app.use('/admin/*', adminRateLimit);

// 搜索接口：防止滥用
app.use('/search/*', searchRateLimit);

// 公开API：基于角色的限制
app.use('/events/*', publicApiRateLimit);
app.use('/venues/*', publicApiRateLimit);
app.use('/circles/*', publicApiRateLimit);
app.use('/artists/*', publicApiRateLimit);

// ------- 基础路由 -------
app.get('/', (c) => c.text('Ayafeed API 服务已启动'));

// 公开业务路由
app.route('/events', eventRoutes);
app.route('/venues', pubVenues);
app.route('/circles', circleRoutes);
app.route('/circles', circleBookmarkRoutes);
app.route('/bookmarks', userBookmarkRoutes);
app.route('/artists', artistRoutes);
app.route('/appearances', appearanceRoutes);
app.route('/search', searchRoutes);
app.route('/feed', feedRoutes);
app.route('/images', imageRoutes);

// 富文本内容路由
app.route('/rich-text', richTextRoutes);

// 后台与认证
app.route('/admin', admin);

// 测试路由 - 检查是否能匹配到 /auth/* 路径
app.get('/auth/test', (c) => {
  console.log('[Test] /auth/test route matched');
  return c.json({ message: 'Test route works' });
});

// Better Auth 路由处理
app.on(['POST', 'GET'], '/auth/*', async (c) => {
  console.log('[Better Auth] Handling request:', c.req.method, c.req.url);
  console.log(
    '[Better Auth] Headers:',
    Object.fromEntries(c.req.raw.headers.entries())
  );

  try {
    const env = c.env as CloudflareBindings;

    // 检查环境变量
    if (!env.BETTER_AUTH_SECRET) {
      console.error(
        '[Better Auth] Missing BETTER_AUTH_SECRET environment variable'
      );
      return c.json({ error: 'Server configuration error' }, 500);
    }

    if (!env.BETTER_AUTH_URL) {
      console.error(
        '[Better Auth] Missing BETTER_AUTH_URL environment variable'
      );
      return c.json({ error: 'Server configuration error' }, 500);
    }

    console.log('[Better Auth] Environment check passed');
    console.log('[Better Auth] BETTER_AUTH_URL:', env.BETTER_AUTH_URL);

    const result = await betterAuth(c.env as CloudflareBindings).handler(
      c.req.raw
    );
    console.log('[Better Auth] Request handled successfully');
    return result;
  } catch (error) {
    console.error('[Better Auth] Error handling request:', error);
    console.error('[Better Auth] Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
    });

    // 返回更详细的错误信息用于调试
    return c.json(
      {
        error: 'Authentication service error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

export { app };
export default app;
