# 用户中心功能说明

## 概述

用户中心是 Ayafeed 平台的个人管理中心，为登录用户提供完整的个人信息管理、偏好设置和活动记录功能。采用现代化的 UI 设计，具有艺术感和良好的用户体验。

## 功能模块

### 1. 个人资料 (`/profile`)
- **用户信息展示**：头像、用户名、角色、邮箱等基本信息
- **个人简介编辑**：支持自定义个人简介
- **账户信息**：注册时间、最后登录时间等
- **活动统计**：收藏数、浏览量、搜索次数等数据概览
- **资料编辑**：支持在线编辑用户名、邮箱、个人简介

### 2. 用户设置 (`/profile/settings`)
- **外观设置**
  - 语言选择：中文、日文、英文
  - 主题设置：明亮、暗黑、跟随系统
- **通知设置**
  - 邮件通知开关
  - 推送通知开关
  - 声音提醒开关
  - 新事件通知
  - 收藏更新通知
- **隐私设置**
  - 个人资料可见性
  - 活动记录可见性
  - 收藏列表可见性
- **账户安全**
  - 密码修改入口

### 3. 收藏管理 (`/profile/bookmarks`)
- **收藏展示**：网格视图和列表视图切换
- **分类筛选**：按事件、社团类型筛选
- **搜索功能**：支持关键词搜索收藏内容
- **排序选项**：按收藏时间、名称排序
- **收藏操作**：查看详情、取消收藏
- **标签系统**：显示收藏项目的相关标签

### 4. 活动记录 (`/profile/activity`)
- **活动统计**：总浏览量、收藏数、搜索次数、平均停留时间
- **活动洞察**：最活跃的一天、最喜欢的类别、活跃度分析
- **时间线展示**：按时间顺序显示用户活动
- **活动类型**：浏览记录、收藏记录、搜索记录、分享记录
- **筛选功能**：按时间范围和活动类型筛选
- **详细信息**：每个活动的具体信息和元数据

## 技术特性

### UI/UX 设计
- **现代化设计**：采用渐变背景、毛玻璃效果、阴影等现代设计元素
- **艺术感**：精心设计的配色方案、图标和动画效果
- **响应式布局**：完美适配桌面端和移动端
- **暗黑模式支持**：自动适配系统主题偏好

### 动画效果
- **页面进入动画**：使用 Motion 库实现流畅的页面切换
- **微交互动画**：按钮悬停、卡片展开等细节动画
- **加载状态**：优雅的加载动画和骨架屏
- **滚动动画**：元素进入视口时的渐入效果

### 技术栈
- **前端框架**：Next.js 15 + React 19
- **状态管理**：Zustand
- **样式方案**：Tailwind CSS
- **动画库**：Motion (Framer Motion)
- **UI 组件**：Radix UI
- **表单处理**：React Hook Form + Zod
- **国际化**：next-intl

## 路由结构

```
/profile                    # 用户中心首页（个人资料）
├── /profile/settings       # 用户设置
├── /profile/bookmarks      # 收藏管理
└── /profile/activity       # 活动记录
```

## 访问控制

- **认证要求**：所有用户中心页面都需要用户登录
- **自动重定向**：未登录用户会被重定向到登录页面
- **权限控制**：基于用户角色显示不同功能

## 导航入口

- **主导航**：顶部用户菜单中的"用户中心"链接
- **侧边栏导航**：用户中心内部的模块导航
- **移动端菜单**：响应式的移动端导航体验

## 数据管理

- **本地存储**：用户偏好设置使用 localStorage 持久化
- **API 集成**：与后端 API 完全集成，支持实时数据同步
- **缓存策略**：使用 TanStack Query 进行数据缓存和状态管理

## 未来扩展

- **社交功能**：关注其他用户、查看动态
- **个性化推荐**：基于用户行为的内容推荐
- **数据导出**：支持导出个人数据
- **第三方集成**：支持社交媒体账户绑定
