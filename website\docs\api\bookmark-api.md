# 收藏模块 API 文档

> 📝 **版本**: v1.0.0  
> 🕒 **更新时间**: 2025-08-02  
> 👥 **目标读者**: 前端开发者、API集成开发者

## 概述

收藏模块提供完整的社团收藏功能，包括收藏状态管理、个性化排序和性能优化。

### 核心功能
- ⭐ 社团收藏/取消收藏
- 📋 用户收藏列表管理
- 📊 收藏统计信息
- 🎯 个性化社团列表排序
- ⚡ 高性能批量查询

## API 端点

### 1. 切换收藏状态

```http
POST /circles/{circleId}/bookmark
```

**功能**: 切换指定社团的收藏状态（收藏/取消收藏）

**认证**: 必需

**参数**:
- `circleId` (path): 社团ID

**响应**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isBookmarked": true
  }
}
```

### 2. 查询收藏状态

```http
GET /circles/{circleId}/bookmark/status
```

**功能**: 查询指定社团的收藏状态

**认证**: 必需

**参数**:
- `circleId` (path): 社团ID

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "isBookmarked": true
  }
}
```

### 3. 获取收藏列表

```http
GET /user/bookmarks
```

**功能**: 获取用户的收藏社团列表

**认证**: 必需

**查询参数**:
- `page` (number, 可选): 页码，默认 1
- `pageSize` (number, 可选): 每页数量，默认 20
- `cursor` (string, 可选): 游标分页标识

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": "circle-123",
        "name": "示例社团",
        "bookmarkedAt": "2025-08-02T10:00:00Z"
      }
    ],
    "total": 42,
    "hasMore": true,
    "nextCursor": "cursor-token"
  }
}
```

### 4. 获取收藏统计

```http
GET /user/bookmarks/stats
```

**功能**: 获取用户收藏统计信息

**认证**: 必需

**查询参数**:
- `includeIds` (boolean, 可选): 是否包含收藏社团ID列表

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalBookmarks": 42,
    "bookmarkedCircleIds": ["circle-123", "circle-456"]
  }
}
```

### 5. 个性化社团列表

```http
GET /circles
```

**功能**: 获取社团列表，支持个性化排序

**认证**: 可选（影响排序）

**查询参数**:
- `page` (number, 可选): 页码，默认 1
- `pageSize` (number, 可选): 每页数量，默认 50
- `search` (string, 可选): 搜索关键词

**个性化特性**:
- 已登录用户：已收藏社团优先显示
- 未登录用户：按默认排序（名称）

**响应**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "items": [
      {
        "id": "circle-123",
        "name": "示例社团",
        "isBookmarked": true
      }
    ],
    "total": 100
  }
}
```

## 性能优化

### 批量查询策略

推荐使用以下策略减少API请求：

```typescript
// ❌ 避免：逐个查询收藏状态
circles.forEach(circle => {
  useBookmarkStatus(circle.id); // 50个请求
});

// ✅ 推荐：批量获取收藏状态
const { data: stats } = useBookmarkStats({ includeIds: true });
const bookmarkedIds = new Set(stats?.bookmarkedCircleIds || []);

circles.forEach(circle => {
  circle.isBookmarked = bookmarkedIds.has(circle.id); // 1个请求
});
```

### 缓存策略

- **个性化结果**: 不使用全局缓存
- **非个性化结果**: 使用5分钟缓存
- **收藏状态**: 实时更新，自动失效缓存

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | 未认证 | 用户需要登录 |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 社团不存在 | 验证社团ID |
| 429 | 请求过频 | 实施请求限流 |

### 错误响应格式

```json
{
  "code": 404,
  "message": "社团不存在",
  "data": null
}
```

## 最佳实践

### 1. 性能优化
- 使用批量查询减少网络请求
- 合理使用缓存策略
- 实施请求去重

### 2. 用户体验
- 提供加载状态指示
- 实施乐观更新
- 优雅的错误处理

### 3. 数据一致性
- 监听收藏状态变化
- 及时更新相关UI
- 处理并发操作

## 相关文档

- [收藏模块前端集成指南](../../收藏模块前端对接文档_v2.md)
- [认证系统文档](./authentication.md)
- [API客户端生成](../frontend/client-generation.md)
