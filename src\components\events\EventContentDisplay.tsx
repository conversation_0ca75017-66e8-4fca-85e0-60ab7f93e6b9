'use client';

import React, { useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useRichTextContent, type EntityType, type Locale } from '@/hooks/useRichTextContent';
import { FileText, Star, BookOpen, Bell, Globe } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EventContentDisplayProps {
  eventId: string;
  className?: string;
}

// 支持的语言配置
const SUPPORTED_LOCALES: { code: Locale; name: string; flag: string }[] = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
];

export function EventContentDisplay({ eventId, className }: EventContentDisplayProps) {
  const currentLocale = useLocale() as Locale;
  const t = useTranslations('richTextEditor');
  const [selectedLocale, setSelectedLocale] = useState<Locale>(currentLocale);

  // 内容类型配置（使用翻译）
  const contentTypes = [
    {
      key: 'introduction' as const,
      label: t('contentTypes.introduction'),
      description: t('placeholders.introduction'),
      icon: FileText,
    },
    {
      key: 'highlights' as const,
      label: t('contentTypes.highlights'),
      description: t('placeholders.highlights'),
      icon: Star,
    },
    {
      key: 'guide' as const,
      label: t('contentTypes.guide'),
      description: t('placeholders.guide'),
      icon: BookOpen,
    },
    {
      key: 'notices' as const,
      label: t('contentTypes.notices'),
      description: t('placeholders.notices'),
      icon: Bell,
    },
  ];

  const {
    content,
    isLoading,
    error,
  } = useRichTextContent({
    entityType: 'event' as EntityType,
    entityId: eventId,
    locale: selectedLocale as Locale,
  });

  // 检查是否有任何内容
  const hasContent = Object.values(content).some(value => value && value.trim().length > 0);

  if (isLoading) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              详细信息
            </CardTitle>
            <div className="text-sm text-muted-foreground">
              {t('status.loading')}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {contentTypes.map((type) => (
                <div key={type.key} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-20 bg-gray-100 rounded"></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              详细信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{t('errors.loadFailed')}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!hasContent) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              详细信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">暂无详细信息</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            详细信息
          </CardTitle>

          {/* 语言选择器 */}
          <div className="flex items-center gap-2 mt-4">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">{t('language.current')}:</span>
            <div className="flex gap-1">
              {SUPPORTED_LOCALES.map((locale) => (
                <button
                  key={locale.code}
                  onClick={() => setSelectedLocale(locale.code)}
                  className={cn(
                    "px-2 py-1 text-xs rounded-md border transition-colors",
                    selectedLocale === locale.code
                      ? "bg-primary text-primary-foreground border-primary"
                      : "bg-background hover:bg-muted border-border"
                  )}
                >
                  {locale.flag} {locale.name}
                </button>
              ))}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="introduction" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              {contentTypes.map((type) => {
                const Icon = type.icon;
                const hasTypeContent = content[type.key] && content[type.key]!.trim().length > 0;

                return (
                  <TabsTrigger
                    key={type.key}
                    value={type.key}
                    disabled={!hasTypeContent}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {type.label}
                    {hasTypeContent && (
                      <Badge variant="secondary" className="ml-1 text-xs">
                        {Math.ceil((content[type.key]?.length || 0) / 100)}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {contentTypes.map((type) => {
              const hasTypeContent = content[type.key] && content[type.key]!.trim().length > 0;

              return (
                <TabsContent key={type.key} value={type.key} className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <type.icon className="h-5 w-5" />
                        {type.label}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">{type.description}</p>
                    </CardHeader>
                    <CardContent>
                      {hasTypeContent ? (
                        <div
                          className="prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{ __html: content[type.key] || '' }}
                        />
                      ) : (
                        <p className="text-muted-foreground">暂无{type.label}信息</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
