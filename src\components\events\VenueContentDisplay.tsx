'use client';

import React, { useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useRichTextContent, type EntityType, type Locale } from '@/hooks/useRichTextContent';
import { FileText, Star, BookOpen, Bell, Globe } from 'lucide-react';

interface VenueContentDisplayProps {
  venueId: string;
  className?: string;
}

export function VenueContentDisplay({ venueId, className }: VenueContentDisplayProps) {
  const locale = useLocale();
  const t = useTranslations('richTextEditor');
  const [selectedLocale, setSelectedLocale] = useState<Locale>(locale as Locale);

  // 内容类型配置（使用翻译）
  const contentTypes = [
    {
      key: 'introduction' as const,
      label: t('contentTypes.introduction'),
      description: t('placeholders.introduction'),
      icon: FileText,
    },
    {
      key: 'highlights' as const,
      label: t('contentTypes.highlights'),
      description: t('placeholders.highlights'),
      icon: Star,
    },
    {
      key: 'guide' as const,
      label: t('contentTypes.guide'),
      description: t('placeholders.guide'),
      icon: BookOpen,
    },
    {
      key: 'notices' as const,
      label: t('contentTypes.notices'),
      description: t('placeholders.notices'),
      icon: Bell,
    },
  ];

  const {
    content,
    isLoading,
    error,
  } = useRichTextContent({
    entityType: 'venue' as EntityType,
    entityId: venueId,
    locale: selectedLocale,
  });

  // 检查是否有任何内容
  const hasContent = Object.values(content).some(value => value && value.trim().length > 0);

  if (isLoading) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              场馆详细信息
            </CardTitle>
            <div className="text-sm text-muted-foreground">
              加载中...
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {contentTypes.map((type) => (
                <div key={type.key} className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-20 bg-gray-100 rounded"></div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              场馆详细信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">加载内容时出错</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!hasContent) {
    return null;
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            场馆详细信息
          </CardTitle>
          
          {/* 语言切换器 */}
          <div className="flex items-center gap-2 mt-4">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <div className="flex gap-1">
              {(['zh', 'ja', 'en'] as const).map((lang) => (
                <Button
                  key={lang}
                  type="button"
                  variant={selectedLocale === lang ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedLocale(lang)}
                  className="h-7 px-2 text-xs"
                >
                  {lang.toUpperCase()}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="introduction" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              {contentTypes.map((type) => {
                const Icon = type.icon;
                const hasTypeContent = content[type.key] && content[type.key]!.trim().length > 0;

                return (
                  <TabsTrigger
                    key={type.key}
                    value={type.key}
                    disabled={!hasTypeContent}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {type.label}
                    {hasTypeContent && (
                      <Badge variant="secondary" className="ml-1 text-xs">
                        {Math.ceil((content[type.key]?.length || 0) / 100)}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {contentTypes.map((type) => {
              const hasTypeContent = content[type.key] && content[type.key]!.trim().length > 0;

              return (
                <TabsContent key={type.key} value={type.key} className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <type.icon className="h-5 w-5" />
                        {type.label}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">{type.description}</p>
                    </CardHeader>
                    <CardContent>
                      {hasTypeContent ? (
                        <div
                          className="prose prose-sm max-w-none"
                          dangerouslySetInnerHTML={{ __html: content[type.key] || '' }}
                        />
                      ) : (
                        <p className="text-muted-foreground">暂无{type.label}信息</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

export default VenueContentDisplay;
