'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useBookmarks, useToggleBookmark } from '@/hooks/useBookmark';
import { useDebounce } from '@/hooks';
import type { GetUserBookmarksResponse } from '@/api/generated/ayafeedComponents';
import { BookmarkHeader } from './BookmarkHeader';
import { BookmarkFilters } from './BookmarkFilters';
import { BookmarkList } from './BookmarkList';

// 转换后的收藏项类型，用于统一显示
interface BookmarkItem {
  id: string;
  type: 'circle'; // 目前只支持社团收藏
  title: string;
  description: string;
  urls: string | null;
  bookmarkedAt: string;
  circleId: string; // 社团ID，用于取消收藏
}

export function UserBookmarks() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('recent');
  const [page, setPage] = useState(1);
  const pageSize = 20;

  // 防抖搜索查询
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // 搜索时重置到第一页
  useEffect(() => {
    setPage(1);
  }, [debouncedSearchQuery]);

  // 获取收藏列表数据
  const {
    data: bookmarksResponse,
    isLoading,
    refetch
  } = useBookmarks({
    page,
    pageSize,
    search: debouncedSearchQuery || undefined,
    sortBy: sortBy === 'recent' ? 'created_at' : 'circle_name',
    sortOrder: sortBy === 'recent' ? 'desc' : 'asc'
  });

  // 取消收藏的 mutation
  const toggleBookmarkMutation = useToggleBookmark();

  // 转换 API 数据为组件需要的格式
  const bookmarks: BookmarkItem[] = useMemo(() => {
    if (!bookmarksResponse?.data?.items) return [];

    return bookmarksResponse.data.items.map(item => ({
      id: item.id,
      type: 'circle' as const,
      title: item.circle.name,
      description: '同人社团',
      urls: item.circle.urls,
      bookmarkedAt: item.created_at,
      circleId: item.circle.id,
    }));
  }, [bookmarksResponse]);

  // 处理取消收藏
  const handleRemoveBookmark = async (circleId: string) => {
    try {
      await toggleBookmarkMutation.mutateAsync({
        pathParams: { circleId }
      });
      // 刷新列表
      refetch();
    } catch (error) {
      console.error('取消收藏失败:', error);
    }
  };

  // 检测是否在搜索中（防抖期间）
  const isSearching = searchQuery !== debouncedSearchQuery;
  const shouldShowLoading = isLoading || isSearching;

  // 直接使用 API 返回的数据
  const filteredBookmarks = bookmarks;

  return (
    <div className="space-y-6">
      <BookmarkHeader
        totalCount={bookmarksResponse?.data?.total || 0}
        isLoading={isLoading}
      />

      <BookmarkFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        sortBy={sortBy}
        onSortChange={setSortBy}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        isLoading={shouldShowLoading}
      />

      <BookmarkList
        items={filteredBookmarks}
        viewMode={viewMode}
        onRemoveBookmark={handleRemoveBookmark}
        isRemoving={toggleBookmarkMutation.isPending}
        searchQuery={searchQuery}
      />
    </div>
  );
}
