# Ayafeed 设计系统使用示例

本文档提供了使用 Ayafeed 设计系统的实际代码示例，帮助开发者快速上手。

## 📄 页面布局示例

### 基础页面结构
```tsx
import { ModernPageLayout, Container, GradientHeading, Subtitle } from '@/components/design-system'

export default function ExamplePage() {
  return (
    <ModernPageLayout>
      <Container>
        {/* 页面标题区域 */}
        <div className="text-center space-y-4 py-16">
          <GradientHeading level={1}>
            页面标题
          </GradientHeading>
          <Subtitle size="lg" className="max-w-2xl mx-auto">
            这里是页面的描述文字，解释页面的主要功能和用途
          </Subtitle>
        </div>
        
        {/* 页面内容 */}
        <div className="space-y-8">
          {/* 内容区域 */}
        </div>
      </Container>
    </ModernPageLayout>
  )
}
```

### 带搜索的列表页面
```tsx
import { useState } from 'react'
import { ModernPageLayout, Container, GradientHeading, ModernInput, ResponsiveGrid, ModernCard } from '@/components/design-system'

export default function ListPage() {
  const [searchQuery, setSearchQuery] = useState('')
  
  return (
    <ModernPageLayout>
      <Container>
        {/* 页面标题 */}
        <div className="text-center space-y-4 py-16">
          <GradientHeading level={1}>
            内容列表
          </GradientHeading>
          <Subtitle size="lg">
            浏览和搜索所有内容
          </Subtitle>
        </div>
        
        {/* 搜索区域 */}
        <div className="flex justify-center mb-8">
          <div className="w-full max-w-md">
            <ModernInput
              type="search"
              placeholder="搜索内容..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        
        {/* 内容网格 */}
        <ResponsiveGrid cols={{ default: 1, sm: 2, md: 3, lg: 4 }}>
          {/* 示例卡片 */}
          <ModernCard>
            <div className="space-y-4">
              <GradientHeading level={3}>
                卡片标题
              </GradientHeading>
              <Subtitle>
                卡片描述内容
              </Subtitle>
            </div>
          </ModernCard>
        </ResponsiveGrid>
      </Container>
    </ModernPageLayout>
  )
}
```

## 🎴 卡片组件示例

### 基础信息卡片
```tsx
import { ModernCard, GradientHeading, Subtitle, ModernButton } from '@/components/design-system'

function InfoCard({ title, description, action }: {
  title: string
  description: string
  action?: () => void
}) {
  return (
    <ModernCard className="space-y-4">
      <GradientHeading level={3}>
        {title}
      </GradientHeading>
      <Subtitle>
        {description}
      </Subtitle>
      {action && (
        <ModernButton onClick={action} variant="primary" size="sm">
          了解更多
        </ModernButton>
      )}
    </ModernCard>
  )
}
```

### 图片展示卡片
```tsx
import { ImageCard, GradientHeading, Subtitle } from '@/components/design-system'
import Image from 'next/image'

function ImageShowcaseCard({ 
  title, 
  description, 
  imageUrl, 
  imageAlt 
}: {
  title: string
  description: string
  imageUrl: string
  imageAlt: string
}) {
  return (
    <ImageCard>
      <div className="relative">
        <Image
          src={imageUrl}
          alt={imageAlt}
          width={400}
          height={240}
          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
        />
      </div>
      <div className="p-4 space-y-2">
        <GradientHeading level={4} className="group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
          {title}
        </GradientHeading>
        <Subtitle size="sm">
          {description}
        </Subtitle>
      </div>
    </ImageCard>
  )
}
```

## 📝 表单组件示例

### 搜索表单
```tsx
import { useState } from 'react'
import { ModernInput, ModernButton } from '@/components/design-system'
import { Search } from 'lucide-react'

function SearchForm({ onSearch }: { onSearch: (query: string) => void }) {
  const [query, setQuery] = useState('')
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(query)
  }
  
  return (
    <form onSubmit={handleSubmit} className="flex gap-2">
      <div className="flex-1 relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-500" />
        <ModernInput
          type="search"
          placeholder="搜索..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-10"
        />
      </div>
      <ModernButton type="submit" variant="primary">
        搜索
      </ModernButton>
    </form>
  )
}
```

### 联系表单
```tsx
import { useState } from 'react'
import { ModernCard, GradientHeading, ModernInput, ModernButton } from '@/components/design-system'

function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // 处理表单提交
    console.log('Form submitted:', formData)
  }
  
  return (
    <ModernCard className="max-w-md mx-auto">
      <div className="space-y-6">
        <GradientHeading level={2} className="text-center">
          联系我们
        </GradientHeading>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">姓名</label>
            <ModernInput
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">邮箱</label>
            <ModernInput
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">消息</label>
            <textarea
              className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 focus:bg-white/80 dark:focus:bg-slate-800/80 focus:border-blue-300 dark:focus:border-blue-600 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 rounded-xl px-4 py-2 w-full min-h-[100px] resize-vertical"
              value={formData.message}
              onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              required
            />
          </div>
          
          <ModernButton type="submit" variant="primary" className="w-full">
            发送消息
          </ModernButton>
        </form>
      </div>
    </ModernCard>
  )
}
```

## 🎨 特殊效果示例

### 悬停卡片网格
```tsx
import { motion } from 'motion/react'
import { ModernCard, GradientHeading, Subtitle } from '@/components/design-system'

function HoverCardGrid({ items }: { items: Array<{ id: string, title: string, description: string }> }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item, index) => (
        <motion.div
          key={item.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.5 }}
        >
          <ModernCard className="h-full">
            <div className="space-y-3">
              <GradientHeading level={4}>
                {item.title}
              </GradientHeading>
              <Subtitle>
                {item.description}
              </Subtitle>
            </div>
          </ModernCard>
        </motion.div>
      ))}
    </div>
  )
}
```

### 渐变按钮组
```tsx
import { ModernButton } from '@/components/design-system'

function ButtonGroup() {
  return (
    <div className="flex flex-wrap gap-4 justify-center">
      <ModernButton variant="primary">
        主要按钮
      </ModernButton>
      <ModernButton variant="secondary">
        次要按钮
      </ModernButton>
      <ModernButton variant="outline">
        轮廓按钮
      </ModernButton>
      <ModernButton variant="ghost">
        幽灵按钮
      </ModernButton>
    </div>
  )
}
```

## 📱 响应式布局示例

### 自适应导航
```tsx
import { useState } from 'react'
import { ModernButton } from '@/components/design-system'
import { Menu, X } from 'lucide-react'

function ResponsiveNavigation() {
  const [isOpen, setIsOpen] = useState(false)
  
  const navItems = [
    { label: '首页', href: '/' },
    { label: '关于', href: '/about' },
    { label: '服务', href: '/services' },
    { label: '联系', href: '/contact' }
  ]
  
  return (
    <nav className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-b border-slate-200/50 dark:border-slate-700/50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="font-bold text-xl">
            Logo
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-4">
            {navItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="px-3 py-2 rounded-lg hover:bg-slate-100/50 dark:hover:bg-slate-700/50 transition-colors"
              >
                {item.label}
              </a>
            ))}
          </div>
          
          {/* Mobile Menu Button */}
          <ModernButton
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X size={20} /> : <Menu size={20} />}
          </ModernButton>
        </div>
        
        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 space-y-2">
            {navItems.map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="block px-3 py-2 rounded-lg hover:bg-slate-100/50 dark:hover:bg-slate-700/50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                {item.label}
              </a>
            ))}
          </div>
        )}
      </div>
    </nav>
  )
}
```

## 🎯 最佳实践提示

### 1. 组件组合
```tsx
// ✅ 好的做法：组合使用设计系统组件
function WellDesignedComponent() {
  return (
    <ModernPageLayout>
      <Container>
        <div className="space-y-8">
          <div className="text-center">
            <GradientHeading level={1}>标题</GradientHeading>
            <Subtitle>描述</Subtitle>
          </div>
          <ResponsiveGrid>
            <ModernCard>内容</ModernCard>
          </ResponsiveGrid>
        </div>
      </Container>
    </ModernPageLayout>
  )
}
```

### 2. 一致的间距
```tsx
// ✅ 使用标准间距类
<div className="space-y-4">  {/* 小间距 */}
<div className="space-y-6">  {/* 中等间距 */}
<div className="space-y-8">  {/* 大间距 */}
```

### 3. 响应式设计
```tsx
// ✅ 移动优先的响应式设计
<div className="text-sm md:text-base lg:text-lg">
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
```

---

💡 **提示**: 这些示例展示了如何正确使用设计系统组件。在实际开发中，请根据具体需求调整和扩展这些模式。
