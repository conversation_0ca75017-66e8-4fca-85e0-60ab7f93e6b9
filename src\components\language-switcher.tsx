'use client'

import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Globe } from 'lucide-react';
import { cn } from "@/lib/utils";
import { useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { SUPPORTED_LOCALES, type Locale, setLocaleToCookie } from '@/lib/locale-utils';
import { createLocaleCacheManager } from '@/lib/locale-cache-utils';
import { RadixButton } from "@/components/ui/radix-components";

// 语言显示名称映射
const LANGUAGE_NAMES: Record<Locale, string> = {
  ja: '日本語',
  en: 'English',
  zh: '中文'
};

// 语言选项组件
interface LanguageOptionProps {
  locale: Locale;
  currentLocale: Locale;
  onSelect: (locale: Locale) => void;
}

function LanguageOption({ locale, currentLocale, onSelect }: LanguageOptionProps) {
  const isActive = locale === currentLocale;

  return (
    <DropdownMenu.Item
      disabled={isActive}
      onSelect={() => onSelect(locale)}
      className={cn(
        "flex items-center justify-between px-3 py-2 text-sm rounded-lg outline-none cursor-pointer transition-all duration-300",
        isActive
          ? "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 text-blue-700 dark:text-blue-300 cursor-default"
          : "hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 focus:bg-gradient-to-r focus:from-blue-50 focus:to-purple-50 dark:focus:from-blue-950 dark:focus:to-purple-950"
      )}
    >
      <span>{LANGUAGE_NAMES[locale]}</span>
      {isActive && (
        <Globe className="h-4 w-4 text-muted-foreground" />
      )}
    </DropdownMenu.Item>
  );
}

// 语言切换器触发按钮组件
function LanguageTrigger() {
  return (
    <DropdownMenu.Trigger asChild>
      <RadixButton
        variant="ghost"
        size="sm"
        className="w-10 h-10 px-0 transition-all text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-gradient-to-r hover:from-slate-100/80 hover:to-slate-50/80 dark:hover:from-slate-800/80 dark:hover:to-slate-700/80 rounded-xl backdrop-blur-sm"
        aria-label="切换语言"
      >
        <Globe className="h-5 w-5 transition-transform hover:scale-110" />
      </RadixButton>
    </DropdownMenu.Trigger>
  );
}

export default function LanguageSwitcher() {
  const locale = useLocale() as Locale;
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleLocaleChange = (nextLocale: Locale) => {
    if (nextLocale === locale) return;

    console.log(`🌐 语言切换开始: ${locale} → ${nextLocale}`);

    // 设置 Cookie
    setLocaleToCookie(nextLocale);

    // 使用智能缓存管理器
    const cacheManager = createLocaleCacheManager(queryClient);
    cacheManager.handleLocaleSwitch(locale, nextLocale);

    // 在开发环境下显示缓存调试信息
    if (process.env.NODE_ENV === 'development') {
      const stats = cacheManager.getCacheStats();
      console.log('📊 缓存统计:', stats);
    }

    // 刷新页面让 next-intl 重新加载
    router.refresh();
  };

  return (
    <DropdownMenu.Root>
      <LanguageTrigger />
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="end"
          className="min-w-[144px] bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 rounded-xl p-2 shadow-xl shadow-slate-200/20 dark:shadow-slate-900/20 z-50"
          sideOffset={5}
        >
          {SUPPORTED_LOCALES.map((supportedLocale) => (
            <LanguageOption
              key={supportedLocale}
              locale={supportedLocale}
              currentLocale={locale}
              onSelect={handleLocaleChange}
            />
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
