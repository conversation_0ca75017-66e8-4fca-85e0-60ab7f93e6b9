'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  usePostRichtextEntityTypeEntityIdContent,
  usePutRichtextEntityTypeEntityIdContent,
  type GetRichtextEntityTypeEntityIdContentResponse,
} from '@/api/generated/ayafeedComponents';
import { type EntityType, type ContentType, type ContentData } from '@/lib/api/content';

export type Locale = 'zh' | 'ja' | 'en';

// 多语言内容数据结构
export interface MultilingualContentData {
  introduction?: Partial<Record<Locale, string>>;
  highlights?: Partial<Record<Locale, string>>;
  guide?: Partial<Record<Locale, string>>;
  notices?: Partial<Record<Locale, string>>;
}

// 变更跟踪
interface ChangeTracker {
  [locale: string]: {
    [contentType: string]: boolean;
  };
}

export interface UseMultilingualContentOptions {
  entityType: EntityType;
  entityId: string;
  initialLocale?: Locale;
}

export function useMultilingualContent({
  entityType,
  entityId,
  initialLocale = 'zh',
}: UseMultilingualContentOptions) {

  const queryClient = useQueryClient();

  // 根据语言生成实际的 entityId（添加语言后缀）
  const getLocalizedEntityId = useCallback((locale: Locale) => {
    return `${entityId}-${locale}`;
  }, [entityId]);
  
  // 状态管理
  const [selectedLocale, setSelectedLocale] = useState<Locale>(initialLocale);
  const [multilingualContent, setMultilingualContent] = useState<MultilingualContentData>({});
  const [changeTracker, setChangeTracker] = useState<ChangeTracker>({});
  
  // 跟踪已加载的语言
  const loadedLocales = useRef<Set<Locale>>(new Set());
  
  // 生成查询键
  const getQueryKey = useCallback((locale: Locale) => ['content', entityType, getLocalizedEntityId(locale)], [entityType, getLocalizedEntityId]);
  
  // 检查是否有未保存的更改
  const hasUnsavedChanges = useCallback((locale?: Locale, contentType?: ContentType) => {
    if (locale && contentType) {
      return changeTracker[locale]?.[contentType] || false;
    }
    
    if (locale) {
      return Object.values(changeTracker[locale] || {}).some(Boolean);
    }
    
    // 检查所有语言的所有内容类型
    return Object.values(changeTracker).some(localeChanges =>
      Object.values(localeChanges).some(Boolean)
    );
  }, [changeTracker]);
  
  // 移除这个函数，因为它违反了 Hook 规则
  
  // 加载指定语言的内容
  const loadContentForLocale = useCallback(async (locale: Locale) => {
    if (loadedLocales.current.has(locale)) {
      console.log('⏭️ useMultilingualContent loadContentForLocale 跳过 (已加载):', locale);
      return;
    }

    console.log('📥 useMultilingualContent loadContentForLocale 开始:', {
      locale,
      entityType,
      entityId
    });

    try {
      // 使用 queryClient 直接调用 API，一次性获取所有内容类型
      const content = await queryClient.fetchQuery({
        queryKey: getQueryKey(locale),
        queryFn: async () => {
          // 使用与生成的 hooks 相同的 fetcher
          const { ayafeedFetch } = await import('@/api/generated/ayafeedFetcher');
          const localizedEntityId = getLocalizedEntityId(locale);
          return ayafeedFetch<GetRichtextEntityTypeEntityIdContentResponse>({
            url: `/rich-text/${entityType}/${localizedEntityId}/content`,
            method: 'GET',
          });
        },
      });

      // 从响应中提取实际的内容数据
      const rawResponse = content as any;

      // 处理 API 响应格式 - 后端返回完整的内容对象
      let contentData;
      if (rawResponse?.data && typeof rawResponse.data === 'object') {
        contentData = rawResponse.data;
      } else {
        // 如果响应格式不符合预期，使用空对象
        contentData = {
          introduction: '',
          highlights: '',
          guide: '',
          notices: ''
        };
      }



      setMultilingualContent(prev => ({
        ...prev,
        introduction: { ...prev.introduction, [locale]: contentData.introduction },
        highlights: { ...prev.highlights, [locale]: contentData.highlights },
        guide: { ...prev.guide, [locale]: contentData.guide },
        notices: { ...prev.notices, [locale]: contentData.notices },
      }));

      // 更新查询缓存
      queryClient.setQueryData(getQueryKey(locale), contentData);
      loadedLocales.current.add(locale);

      console.log('✅ useMultilingualContent loadContentForLocale 成功:', locale);
    } catch (error) {
      console.error(`❌ useMultilingualContent loadContentForLocale 失败 ${locale}:`, error);
      // 对于语言切换，如果内容不存在是正常情况，不需要抛出错误
      // 只有在明确的网络错误或服务器错误时才抛出
      if (error && typeof error === 'object' && 'status' in error && error.status !== 404) {
        throw error;
      }
      // 404 错误表示该语言还没有内容，这是正常情况
      console.log(`No content found for locale ${locale}, initializing empty content`);
    }
  }, [entityType, entityId, queryClient, getQueryKey]);
  
  // 获取当前内容
  const getCurrentContent = useCallback((contentType: ContentType, locale?: Locale) => {
    const targetLocale = locale || selectedLocale;
    return multilingualContent[contentType]?.[targetLocale] || '';
  }, [multilingualContent, selectedLocale]);
  
  // 更新内容
  const updateContent = useCallback((contentType: ContentType, value: string, locale?: Locale) => {
    const targetLocale = locale || selectedLocale;

    setMultilingualContent(prev => ({
      ...prev,
      [contentType]: {
        ...prev[contentType],
        [targetLocale]: value,
      },
    }));

    // 标记为已更改
    setChangeTracker(prev => ({
      ...prev,
      [targetLocale]: {
        ...prev[targetLocale],
        [contentType]: true,
      },
    }));
  }, [selectedLocale]);
  
  // 保存单个内容
  const saveSingleMutation = usePostRichtextEntityTypeEntityIdContent({
    onSuccess: (data, variables) => {
      console.log('💾 单个内容保存成功:', { data, variables });
      const { body } = variables;
      const { content_type: contentType } = body;
      // 从请求头中获取语言信息，或使用当前选中的语言
      const locale = selectedLocale;
      // 清除变更标记
      setChangeTracker(prev => ({
        ...prev,
        [locale]: {
          ...prev[locale],
          [contentType]: false,
        },
      }));

      // 更新查询缓存
      const queryKey = getQueryKey(locale);
      queryClient.setQueryData(queryKey, (oldData: ContentData | undefined) => ({
        ...oldData,
        [contentType]: getCurrentContent(contentType, locale),
      }));

      toast.success('内容保存成功');
    },
    onError: (error, variables) => {
      console.error('❌ 单个内容保存失败:', { error, variables });
      toast.error('保存失败');
    },
  });
  
  // 批量保存所有内容
  const saveAllMutation = usePutRichtextEntityTypeEntityIdContent({
    onSuccess: (data, variables) => {
      console.log('💾 批量内容保存成功:', { data, variables });
      const locale = selectedLocale;
      // 清除当前语言的变更标记
      setChangeTracker(prev => ({
        ...prev,
        [locale]: {},
      }));
      toast.success('所有内容保存成功');
    },
    onError: (error, variables) => {
      console.error('❌ 批量内容保存失败:', { error, variables });
      toast.error('保存失败');
    },
  });
  
  // 重置内容
  const resetContent = useCallback(async (locale?: Locale) => {
    const targetLocale = locale || selectedLocale;
    
    // 重新加载内容
    loadedLocales.current.delete(targetLocale);
    await loadContentForLocale(targetLocale);
    
    // 清除变更标记
    setChangeTracker(prev => ({
      ...prev,
      [targetLocale]: {},
    }));
  }, [selectedLocale, loadContentForLocale]);
  
  // 切换语言
  const switchLocale = useCallback(async (locale: Locale) => {
    console.log('🔄 useMultilingualContent switchLocale 开始:', {
      from: selectedLocale,
      to: locale,
      entityType,
      entityId,
      alreadyLoaded: loadedLocales.current.has(locale)
    });

    if (!loadedLocales.current.has(locale)) {
      await loadContentForLocale(locale);
    }
    setSelectedLocale(locale);

    console.log('✅ useMultilingualContent switchLocale 完成');
  }, [loadContentForLocale, selectedLocale, entityType, entityId]);

  // 初始化：自动加载当前语言的内容
  useEffect(() => {
    console.log('🔄 useMultilingualContent 初始化加载:', { selectedLocale, entityType, entityId });
    if (!loadedLocales.current.has(selectedLocale)) {
      loadContentForLocale(selectedLocale);
    }
  }, [selectedLocale, entityType, entityId, loadContentForLocale]);

  return {
    // 状态
    selectedLocale,
    multilingualContent,
    loadedLocales: loadedLocales.current,
    
    // 查询状态
    isLoading: saveSingleMutation.isPending || saveAllMutation.isPending,
    isSaving: saveSingleMutation.isPending || saveAllMutation.isPending,
    
    // 内容操作
    getCurrentContent,
    updateContent,
    loadContentForLocale,
    switchLocale,
    
    // 保存操作
    saveSingle: (contentType: ContentType, locale?: Locale) => {
      const targetLocale = locale || selectedLocale;
      const content = getCurrentContent(contentType, targetLocale);
      console.log('💾 保存单个内容:', {
        contentType,
        targetLocale,
        content,
        entityType,
        entityId
      });
      const localizedEntityId = getLocalizedEntityId(targetLocale);
      return saveSingleMutation.mutateAsync({
        pathParams: {
          entityType,
          entityId: localizedEntityId,
        },
        body: {
          entity_type: entityType,
          entity_id: localizedEntityId,
          content_type: contentType,
          content,
        },
      });
    },
    saveAll: (locale?: Locale) => {
      const targetLocale = locale || selectedLocale;
      const localizedEntityId = getLocalizedEntityId(targetLocale);
      const contentData = {
        introduction: multilingualContent.introduction?.[targetLocale] || '',
        highlights: multilingualContent.highlights?.[targetLocale] || '',
        guide: multilingualContent.guide?.[targetLocale] || '',
        notices: multilingualContent.notices?.[targetLocale] || '',
      };
      return saveAllMutation.mutateAsync({
        pathParams: {
          entityType,
          entityId: localizedEntityId,
        },
        body: contentData,
      });
    },
    
    // 其他操作
    resetContent,
    hasUnsavedChanges,
  };
}
