#!/usr/bin/env node

/**
 * Better Auth 测试脚本
 * 用于验证注册和登录功能
 */

const API_BASE = 'http://127.0.0.1:10618';

async function testSignUp() {
  console.log('🧪 测试用户注册...');

  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User',
  };

  try {
    const response = await fetch(`${API_BASE}/auth/sign-up/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser),
    });

    console.log('📊 响应状态:', response.status);
    console.log('📊 响应头:', Object.fromEntries(response.headers.entries()));

    const data = await response.text();
    console.log('📊 响应内容:', data);

    if (response.ok) {
      console.log('✅ 注册成功!');
      return JSON.parse(data);
    } else {
      console.log('❌ 注册失败:', response.status, data);
      return null;
    }
  } catch (error) {
    console.error('❌ 请求错误:', error.message);
    return null;
  }
}

async function testSignIn() {
  console.log('\n🧪 测试用户登录...');

  const credentials = {
    email: '<EMAIL>',
    password: 'password123',
  };

  try {
    const response = await fetch(`${API_BASE}/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    console.log('📊 响应状态:', response.status);
    const data = await response.text();
    console.log('📊 响应内容:', data);

    if (response.ok) {
      console.log('✅ 登录成功!');
      return JSON.parse(data);
    } else {
      console.log('❌ 登录失败:', response.status, data);
      return null;
    }
  } catch (error) {
    console.error('❌ 请求错误:', error.message);
    return null;
  }
}

async function testHealthCheck() {
  console.log('🧪 测试服务健康状态...');

  try {
    const response = await fetch(`${API_BASE}/`);
    const data = await response.text();

    console.log('📊 健康检查状态:', response.status);
    console.log('📊 健康检查响应:', data);

    return response.ok;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 开始 Better Auth 测试\n');

  // 1. 健康检查
  const isHealthy = await testHealthCheck();
  if (!isHealthy) {
    console.log('❌ 服务不可用，请确保服务器正在运行');
    process.exit(1);
  }

  // 2. 测试注册
  const signUpResult = await testSignUp();

  // 3. 测试登录
  if (signUpResult) {
    await testSignIn();
  }

  console.log('\n🏁 测试完成');
}

main().catch(console.error);
