# ADR-0005: 用户个人资料和安全功能扩展

日期: 2025-01-02

## 状态

提议

## 背景

随着 Ayafeed 平台用户基数的增长，我们收到了用户对个人账户管理功能的强烈需求：

1. **密码管理需求**：用户希望能够修改密码以提高账户安全性
2. **邮箱绑定需求**：用户需要绑定邮箱用于账户恢复和重要通知
3. **数据模型限制**：当前用户表缺少 email 字段，无法支持邮箱相关功能
4. **安全性考虑**：需要建立完善的账户安全管理机制
5. **用户体验**：缺少统一的个人设置管理界面

### 当前系统状态

- 用户表结构：`auth_user(id, username, role, locale)`
- 认证方式：基于 Cookie 的会话管理
- 密码存储：独立的 `auth_key` 表
- 缺失功能：邮箱管理、密码修改、个人资料页面

## 决策

我们决定实现**渐进式用户个人资料和安全功能扩展**，分阶段添加用户账户管理功能：

### 1. 数据库扩展策略

**扩展用户表结构**：
```sql
-- 为 auth_user 表添加 email 字段
ALTER TABLE auth_user ADD COLUMN email TEXT UNIQUE;
ALTER TABLE auth_user ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE auth_user ADD COLUMN updated_at TEXT DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'));

-- 创建邮箱验证表（可选功能）
CREATE TABLE email_verifications (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES auth_user(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  token TEXT NOT NULL UNIQUE,
  expires_at TEXT NOT NULL,
  verified_at TEXT,
  created_at TEXT NOT NULL DEFAULT (strftime('%Y-%m-%dT%H:%M:%fZ', 'now'))
);
```

### 2. API 设计策略

**遵循 RESTful 设计原则**：
```typescript
// 密码管理
PUT    /user/security/password     // 修改密码
POST   /user/security/password/reset // 重置密码（未来功能）

// 邮箱管理  
PUT    /user/profile/email         // 绑定/修改邮箱
POST   /user/profile/email/verify  // 发送验证邮件
PUT    /user/profile/email/verify  // 验证邮箱

// 个人资料
GET    /user/profile               // 获取个人资料
PUT    /user/profile               // 更新个人资料
```

### 3. 安全设计原则

**多层安全验证**：
- 密码修改需要验证当前密码
- 邮箱修改支持验证流程（可选）
- 所有操作记录安全日志
- 实施操作频率限制

### 4. 前端架构策略

**模块化组件设计**：
```typescript
// 页面结构
/settings
  ├── /profile          // 个人资料
  ├── /security         // 安全设置
  └── /privacy          // 隐私设置（未来）

// 组件结构
components/settings/
  ├── ProfileForm.tsx   // 个人资料表单
  ├── PasswordForm.tsx  // 密码修改表单
  ├── EmailForm.tsx     // 邮箱管理表单
  └── SecurityLog.tsx   // 安全日志（未来）
```

## 实施计划

### 阶段一：核心功能（优先级：高）
- [ ] 数据库迁移：添加 email 字段
- [ ] API 实现：密码修改功能
- [ ] API 实现：邮箱绑定功能
- [ ] 前端：用户设置页面基础框架
- [ ] 前端：密码修改表单
- [ ] 前端：邮箱绑定表单

### 阶段二：增强功能（优先级：中）
- [ ] 邮箱验证流程
- [ ] 操作安全日志
- [ ] 个人资料扩展字段
- [ ] 头像上传功能

### 阶段三：高级功能（优先级：低）
- [ ] 两步验证
- [ ] 登录设备管理
- [ ] 密码重置功能
- [ ] 账户删除功能

## 后果

### 积极影响

1. **用户体验提升**
   - 用户可以自主管理账户安全
   - 提供完整的个人资料管理体验
   - 增强用户对平台的信任度

2. **安全性增强**
   - 支持定期密码更新
   - 邮箱绑定提供账户恢复途径
   - 建立完善的安全操作记录

3. **平台完整性**
   - 符合现代 Web 应用的标准功能
   - 为未来高级功能奠定基础
   - 提升平台的专业形象

4. **开发效率**
   - 基于现有架构，开发风险低
   - 可复用现有认证和权限系统
   - 模块化设计便于维护

### 潜在风险

1. **数据迁移风险**
   - 现有用户数据需要平滑迁移
   - 需要处理 email 字段的空值情况

2. **安全复杂性**
   - 新增攻击面（密码修改、邮箱验证）
   - 需要实施适当的安全措施

3. **用户体验一致性**
   - 需要与现有 UI 风格保持一致
   - 错误处理和反馈机制要统一

### 缓解措施

1. **数据安全**
   - 实施数据库备份策略
   - 使用事务确保数据一致性
   - 渐进式迁移，支持回滚

2. **安全加固**
   - 实施操作频率限制
   - 添加 CSRF 保护
   - 记录详细的安全日志

3. **质量保证**
   - 编写全面的单元测试
   - 进行安全性测试
   - 用户验收测试

## 备选方案

### 方案A：第三方认证集成
**描述**：集成 OAuth 提供商（Google、GitHub 等）
**优势**：减少开发工作量，提高安全性
**劣势**：增加外部依赖，用户体验不一致

### 方案B：微服务拆分
**描述**：将用户管理拆分为独立的微服务
**优势**：更好的可扩展性和独立性
**劣势**：增加系统复杂性，当前规模不需要

### 方案C：延迟实施
**描述**：暂时不实施，等待更多用户反馈
**优势**：避免过早优化
**劣势**：错失提升用户体验的机会

## 相关决策

- [ADR-0002: Cookie-based Authentication](./0002-cookie-based-auth.md)
- [ADR-0004: 双重权限保护机制](./0004-dual-layer-auth-protection.md)

## 参考资料

- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [NIST Digital Identity Guidelines](https://pages.nist.gov/800-63-3/)
- [Next.js Authentication Patterns](https://nextjs.org/docs/app/building-your-application/authentication)
- [用户中心功能规划文档](../user-center-specification.md)
