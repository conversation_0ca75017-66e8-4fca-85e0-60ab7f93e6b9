// The shape of the environment variables for the Hono app
export type Env = {
  Bindings: CloudflareBindings;
};

// Shape of the auth context set by the middleware
export type AuthContext = {
  user: {
    id: string;
    username: string;
  } | null;
  session: {
    id: string;
  } | null;
};

// Define the full Hono app type by extending the base env
export type HonoApp = {
  Bindings: CloudflareBindings;
  Variables: {
    auth: AuthContext;
  };
};

// Global type declarations for Cloudflare Workers environment
declare global {
  // Base64 encoding/decoding functions available in Cloudflare Workers
  function btoa(data: string): string;
  function atob(data: string): string;
}

export {};
