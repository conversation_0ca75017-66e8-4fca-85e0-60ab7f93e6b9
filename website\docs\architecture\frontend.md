# 前端架构

> Ayafeed 前端应用的详细架构设计和实现规范。

## 技术栈

### 核心框架
- **React 19**: 最新的 React 版本，支持并发特性
- **Next.js 15**: 使用 App Router，支持 SSR/SSG
- **TypeScript**: 严格的类型检查，提升代码质量

### UI 和样式
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Radix UI**: 无样式的可访问组件库
- **Lucide React**: 现代化图标库
- **class-variance-authority**: 组件变体管理

### 状态管理
- **React Query v5**: 服务器状态管理和缓存
- **Zustand**: 轻量级客户端状态管理
- **React Hook Form**: 表单状态管理

### 开发工具
- **Vitest**: 快速的单元测试框架
- **ESLint**: 代码质量检查
- **Husky**: Git hooks 管理

## 项目结构

```text
src/
├── app/                    # Next.js App Router 页面
│   ├── [locale]/          # 国际化路由
│   ├── globals.css        # 全局样式
│   └── layout.tsx         # 根布局
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   └── examples/         # 示例组件
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具库
│   └── api/             # API 相关工具
├── services/             # 业务服务层
├── stores/               # Zustand 状态管理
├── types/                # TypeScript 类型定义
├── utils/                # 工具函数
└── styles/               # 样式文件
```

## 架构原则

### 1. 功能模块化
- 按业务功能组织代码（events、circles、admin 等）
- 每个模块包含相关的组件、hooks 和类型定义
- 模块间通过明确的接口进行通信

### 2. 类型安全
- 基于 OpenAPI 规范自动生成 API 类型
- 严格的 TypeScript 配置
- 运行时类型验证（Zod）

### 3. 性能优化
- 基于路由的代码分割
- React Suspense + loading.tsx 优化加载体验
- React Query 缓存策略
- 图片懒加载和优化

### 4. 可访问性
- 使用 Radix UI 确保组件可访问性
- 语义化 HTML 结构
- 键盘导航支持
- 屏幕阅读器友好

## 开发规范

### 组件设计
- 优先使用函数组件和 Hooks
- 组件职责单一，易于测试
- 使用 TypeScript 接口定义 Props
- 支持 ref 转发和可访问性属性

### 状态管理
- 服务器状态使用 React Query
- 客户端状态使用 Zustand
- 表单状态使用 React Hook Form
- 避免不必要的全局状态

### 样式管理
- 使用 Tailwind CSS 实用类
- 组件变体使用 class-variance-authority
- 响应式设计优先
- 深色模式支持