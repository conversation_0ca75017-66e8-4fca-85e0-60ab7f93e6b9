"use client"

import { skipToken } from "@tanstack/react-query";
import { useParams } from "next/navigation";

import {
  useGetCirclesId,
  useGetAppearances,
} from "@/api/generated/ayafeedComponents";
import CircleHeader from "@/components/circles/CircleHeader";
import AppearancesGrid from "@/components/circles/AppearancesGrid";

export default function CircleDetailPage() {
  const { id } = useParams<{ id: string }>();

  // 基本社团信息
  const { data: circle } = useGetCirclesId(
    id ? { pathParams: { id } } : skipToken,
    {
      // 后端部分字段可能为 null，需要转换类型
      select: (data) =>
        data
          ? {
              ...data,
              urls: data.urls ?? "{}",
            }
          : null,
    },
  );

  // 参展记录（分页上限 500 足够）
  const { data: appearancesRes } = useGetAppearances(
    id
      ? { queryParams: { circle_id: id, page: "1", pageSize: "500" } }
      : skipToken,
    {
      select: (data) => data?.items ?? [],
      placeholderData: (prev) => prev,
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    },
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/5 to-blue-600/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative">
        {/* 页头：社团信息 */}
        <CircleHeader circle={circle ?? null} />

        <main className="max-w-7xl mx-auto flex flex-col gap-10 px-4 py-10">
          <section className="flex flex-col gap-6">
            <h2 className="text-2xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
              参展记录
            </h2>
            <AppearancesGrid data={(appearancesRes ?? []) as any} />
          </section>
        </main>
      </div>
    </div>
  );
} 