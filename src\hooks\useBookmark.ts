import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useMemo, useCallback } from 'react';
import { toast } from 'sonner';
import { getCurrentLocale } from '@/lib/locale-utils';
import {
  showBookmarkError,
  showBookmarkSuccess,
  logBookmarkError,
  type BookmarkError,
} from '@/lib/bookmark-errors';

// 使用生成的 API 客户端
import {
  usePostCirclesCircleIdBookmark,
  useGetCirclesCircleIdBookmarkStatus,
  useGetUserBookmarks,
  useGetUserBookmarksStats,
  type PostCirclesCircleIdBookmarkVariables,
  type GetCirclesCircleIdBookmarkStatusVariables,
  type GetUserBookmarksVariables,
  type GetUserBookmarksStatsVariables,
  type PostCirclesCircleIdBookmarkResponse,
  type GetCirclesCircleIdBookmarkStatusResponse,
  type GetUserBookmarksResponse,
  type GetUserBookmarksStatsResponse,
} from '@/api/generated/ayafeedComponents';

/**
 * 切换收藏状态 Hook
 * 提供乐观更新和错误处理
 */
export function useToggleBookmark() {
  const queryClient = useQueryClient();
  const currentLocale = getCurrentLocale();

  return usePostCirclesCircleIdBookmark({
    onMutate: async (variables) => {
      const circleId = variables.pathParams.circleId;

      // 取消正在进行的查询避免竞态条件
      await queryClient.cancelQueries({
        queryKey: ['getCirclesCircleIdBookmarkStatus', { pathParams: { circleId } }]
      });
      // 取消所有收藏相关的查询
      await queryClient.cancelQueries({
        predicate: (query) => JSON.stringify(query.queryKey).includes('bookmarks')
      });

      // 获取当前状态快照
      const previousStatus = queryClient.getQueryData<GetCirclesCircleIdBookmarkStatusResponse>([
        'getCirclesCircleIdBookmarkStatus',
        { pathParams: { circleId } }
      ]);

      // 乐观更新单个收藏状态
      if (previousStatus?.data) {
        queryClient.setQueryData<GetCirclesCircleIdBookmarkStatusResponse>([
          'getCirclesCircleIdBookmarkStatus',
          { pathParams: { circleId } }
        ], {
          ...previousStatus,
          data: {
            ...previousStatus.data,
            isBookmarked: !previousStatus.data.isBookmarked,
            bookmarkId: !previousStatus.data.isBookmarked ? 'temp-id' : null,
            createdAt: !previousStatus.data.isBookmarked ? new Date().toISOString() : null,
          }
        });
      }

      return { previousStatus, circleId };
    },
    
    onError: (error, variables, context) => {
      const circleId = variables.pathParams.circleId;

      // 回滚乐观更新
      if (context && typeof context === 'object' && 'previousStatus' in context && 'circleId' in context) {
        queryClient.setQueryData([
          'getCirclesCircleIdBookmarkStatus',
          { pathParams: { circleId: context.circleId as string } }
        ], context.previousStatus);
      }

      // 记录错误日志
      logBookmarkError(error as BookmarkError, {
        action: 'toggle_bookmark',
        circleId,
      });

      // 显示错误提示
      showBookmarkError(error as BookmarkError, 'Bookmark toggle');
    },
    
    onSuccess: (data, variables) => {
      const circleId = variables.pathParams.circleId;
      const message = data.data?.isBookmarked ? 'Added to bookmarks' : 'Removed from bookmarks';

      // 显示成功提示
      showBookmarkSuccess(message);

      // 刷新所有相关查询 - 使用更广泛的匹配模式
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          // 匹配所有包含 'bookmarks' 的查询
          return JSON.stringify(queryKey).includes('bookmarks');
        }
      });
    },
    
    onSettled: (data, error, variables) => {
      const circleId = variables.pathParams.circleId;
      // 确保数据一致性
      queryClient.invalidateQueries({ 
        queryKey: ['getCirclesCircleIdBookmarkStatus', { pathParams: { circleId } }] 
      });
    },
  });
}

/**
 * 检查收藏状态 Hook
 */
export function useBookmarkStatus(circleId: string, enabled: boolean = true) {
  return useGetCirclesCircleIdBookmarkStatus(
    { pathParams: { circleId } },
    {
      enabled: enabled && !!circleId,
      staleTime: 10 * 60 * 1000, // 10分钟缓存
      retry: (failureCount, error: any) => {
        // 认证错误不重试
        if (error?.status === 401) return false;
        return failureCount < 3;
      },
    }
  );
}

/**
 * 获取收藏列表 Hook
 */
export function useBookmarks(
  query: Partial<GetUserBookmarksVariables['queryParams']> = {},
  enabled: boolean = true
) {
  return useGetUserBookmarks(
    { queryParams: query },
    {
      enabled,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      placeholderData: (previousData) => previousData, // React Query v5 新语法，保持之前的数据
      retry: (failureCount, error: any) => {
        if (error?.status === 401) return false;
        return failureCount < 3;
      },
    }
  );
}

/**
 * 获取收藏统计 Hook
 */
export function useBookmarkStats(enabled: boolean = true) {
  return useGetUserBookmarksStats(
    {},
    {
      enabled,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      retry: (failureCount, error: any) => {
        if (error?.status === 401) return false;
        return failureCount < 3;
      },
    }
  );
}

/**
 * 🚀 性能优化：批量收藏状态管理器
 * 使用收藏统计接口获取所有收藏的社团ID，实现高效的批量状态检查
 */
export function useBookmarkManager() {
  const queryClient = useQueryClient();

  // 获取收藏统计（包含ID列表）
  const { data: stats, isLoading } = useGetUserBookmarksStats(
    { queryParams: { includeIds: "true" } },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
      retry: (failureCount, error: any) => {
        if (error?.status === 401) return false;
        return failureCount < 3;
      },
    }
  );

  // 创建收藏状态映射
  const bookmarkedIds = useMemo(() => {
    return new Set(stats?.data?.bookmarkedCircleIds || []);
  }, [stats?.data?.bookmarkedCircleIds]);

  // 快速查询收藏状态
  const isBookmarked = useCallback(
    (circleId: string) => {
      return bookmarkedIds.has(circleId);
    },
    [bookmarkedIds]
  );

  return {
    isBookmarked,
    bookmarkedIds,
    stats: stats?.data,
    isLoading,
  };
}

/**
 * 无限滚动收藏列表 Hook
 */
export function useInfiniteBookmarks(
  baseQuery: Partial<GetUserBookmarksVariables['queryParams']> = {}
) {
  const queryClient = useQueryClient();
  
  // 注意：这里需要根据实际的无限查询实现来调整
  // 当前生成的代码可能不直接支持无限查询，需要手动实现
  return useQuery({
    queryKey: ['getUserBookmarks', 'infinite', baseQuery],
    queryFn: async ({ pageParam = undefined }) => {
      // 这里需要根据实际的游标分页实现
      const query = {
        ...baseQuery,
        cursor: pageParam,
        pageSize: 20,
      };
      
      // 使用生成的 fetcher 函数
      // 注意：这里可能需要直接调用 fetch 函数而不是 hook
      throw new Error('Infinite query implementation needed');
    },
    enabled: false, // 暂时禁用，等待实现
  });
}

/**
 * 错误消息处理函数
 */
function getBookmarkErrorMessage(error: any): string {
  if (error?.status === 401) {
    return 'Please log in to bookmark circles';
  }
  if (error?.status === 404) {
    return 'Circle not found';
  }
  if (error?.payload?.code === 20001) {
    return 'Authentication required';
  }
  if (error?.payload?.code === 40001) {
    return 'Invalid circle ID';
  }
  if (error?.payload?.code === 10002) {
    return 'Circle does not exist';
  }
  if (error?.payload?.code === 50001) {
    return 'Server error, please try again';
  }
  return 'Operation failed, please try again';
}

/**
 * 验证 circleId 格式
 */
export function validateCircleId(circleId: string): boolean {
  return /^[a-zA-Z0-9-_]+$/.test(circleId) && circleId.length > 0;
}

/**
 * 类型导出，方便其他组件使用
 */
export type {
  PostCirclesCircleIdBookmarkResponse,
  GetCirclesCircleIdBookmarkStatusResponse,
  GetUserBookmarksResponse,
  GetUserBookmarksStatsResponse,
};
