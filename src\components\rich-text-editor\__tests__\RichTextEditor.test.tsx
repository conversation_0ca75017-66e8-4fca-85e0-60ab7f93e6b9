import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';

import { RichTextEditor } from '../RichTextEditor';

// Mock Tiptap
vi.mock('@tiptap/react', () => ({
  useEditor: vi.fn(() => ({
    getHTML: vi.fn(() => '<p>Test content</p>'),
    commands: {
      setContent: vi.fn(),
    },
    isActive: vi.fn(() => false),
    can: vi.fn(() => ({
      undo: vi.fn(() => true),
      redo: vi.fn(() => true),
    })),
    chain: vi.fn(() => ({
      focus: vi.fn(() => ({
        toggleBold: vi.fn(() => ({ run: vi.fn() })),
        toggleItalic: vi.fn(() => ({ run: vi.fn() })),
        undo: vi.fn(() => ({ run: vi.fn() })),
        redo: vi.fn(() => ({ run: vi.fn() })),
      })),
    })),
  })),
  EditorContent: ({ editor }: any) => <div data-testid="editor-content">Editor Content</div>,
}));

// Mock extensions
vi.mock('@tiptap/starter-kit', () => ({
  default: {
    configure: vi.fn(() => ({})),
  },
}));

vi.mock('@tiptap/extension-image', () => ({
  default: {
    configure: vi.fn(() => ({})),
  },
}));

vi.mock('@tiptap/extension-link', () => ({
  default: {
    configure: vi.fn(() => ({})),
  },
}));

vi.mock('@tiptap/extension-text-align', () => ({
  default: {
    configure: vi.fn(() => ({})),
  },
}));

vi.mock('@tiptap/extension-color', () => ({
  default: {},
}));

vi.mock('@tiptap/extension-text-style', () => ({
  default: {},
}));

vi.mock('@tiptap/extension-bullet-list', () => ({
  default: {
    configure: vi.fn(() => ({})),
  },
}));

vi.mock('@tiptap/extension-ordered-list', () => ({
  default: {
    configure: vi.fn(() => ({})),
  },
}));

vi.mock('@tiptap/extension-list-item', () => ({
  default: {},
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('RichTextEditor', () => {
  it('renders editor with toolbar', () => {
    const mockOnChange = vi.fn();
    
    renderWithProviders(
      <RichTextEditor
        content="<p>Initial content</p>"
        onChange={mockOnChange}
        placeholder="Start writing..."
      />
    );

    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('calls onChange when content changes', async () => {
    const mockOnChange = vi.fn();
    
    renderWithProviders(
      <RichTextEditor
        content=""
        onChange={mockOnChange}
        placeholder="Start writing..."
      />
    );

    // The onChange should be called through the editor's onUpdate callback
    // This is mocked in our useEditor mock
    expect(screen.getByTestId('editor-content')).toBeInTheDocument();
  });

  it('shows save button when onSave is provided', () => {
    const mockOnSave = vi.fn();
    const mockOnChange = vi.fn();
    
    renderWithProviders(
      <RichTextEditor
        content=""
        onChange={mockOnChange}
        onSave={mockOnSave}
        placeholder="Start writing..."
      />
    );

    expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
  });

  it('disables editor when editable is false', () => {
    const mockOnChange = vi.fn();
    
    renderWithProviders(
      <RichTextEditor
        content="<p>Read only content</p>"
        onChange={mockOnChange}
        editable={false}
        placeholder="Start writing..."
      />
    );

    const editorContent = screen.getByTestId('editor-content');
    expect(editorContent).toBeInTheDocument();
  });

  it('shows loading state when editor is not ready', () => {
    // Mock useEditor to return null (loading state)
    vi.doMock('@tiptap/react', () => ({
      useEditor: vi.fn(() => null),
      EditorContent: ({ editor }: any) => <div data-testid="editor-content">Editor Content</div>,
    }));

    const mockOnChange = vi.fn();

    renderWithProviders(
      <RichTextEditor
        content=""
        onChange={mockOnChange}
        placeholder="Start writing..."
      />
    );

    // Should show loading skeleton
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });
});
